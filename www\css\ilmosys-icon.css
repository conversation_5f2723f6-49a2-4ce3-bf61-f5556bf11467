@font-face {
  font-family: 'ilmosys-icon';
  src:  url('../fonts/ilmosys-icon.eot?pqxsa1');
  src:  url('../fonts/ilmosys-icon.eot?pqxsa1#iefix') format('embedded-opentype'),
    url('../fonts/ilmosys-icon.ttf?pqxsa1') format('truetype'),
    url('../fonts/ilmosys-icon.woff?pqxsa1') format('woff'),
    url('../fonts/ilmosys-icon.svg?pqxsa1#ilmosys-icon') format('svg');
  font-weight: normal;
  font-style: normal;
}

i {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'ilmosys-icon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ilmosys-aa:before {
  content: "\e900";
}
.ilmosys-add:before {
  content: "\e901";
}
.ilmosys-add-bag:before {
  content: "\e902";
}
.ilmosys-add-basket:before {
  content: "\e903";
}
.ilmosys-add-cart:before {
  content: "\e904";
}
.ilmosys-add-file:before {
  content: "\e905";
}
.ilmosys-address-book:before {
  content: "\e906";
}
.ilmosys-address-book2:before {
  content: "\e907";
}
.ilmosys-add-spaceafterparagraph:before {
  content: "\e908";
}
.ilmosys-add-spacebeforeparagraph:before {
  content: "\e909";
}
.ilmosys-add-user:before {
  content: "\e90a";
}
.ilmosys-add-userstar:before {
  content: "\e90b";
}
.ilmosys-add-window:before {
  content: "\e90c";
}
.ilmosys-administrator:before {
  content: "\e90d";
}
.ilmosys-aerobics:before {
  content: "\e90e";
}
.ilmosys-aerobics-2:before {
  content: "\e90f";
}
.ilmosys-aerobics-3:before {
  content: "\e910";
}
.ilmosys-affiliate:before {
  content: "\e911";
}
.ilmosys-air-balloon:before {
  content: "\e912";
}
.ilmosys-airbrush:before {
  content: "\e913";
}
.ilmosys-airship:before {
  content: "\e914";
}
.ilmosys-alarm:before {
  content: "\e915";
}
.ilmosys-alarm-clock:before {
  content: "\e916";
}
.ilmosys-alarm-clock2:before {
  content: "\e917";
}
.ilmosys-alien:before {
  content: "\e918";
}
.ilmosys-alien-2:before {
  content: "\e919";
}
.ilmosys-aligator:before {
  content: "\e91a";
}
.ilmosys-align-center:before {
  content: "\e91b";
}
.ilmosys-align-justifyall:before {
  content: "\e91c";
}
.ilmosys-align-justifycenter:before {
  content: "\e91d";
}
.ilmosys-align-justifyleft:before {
  content: "\e91e";
}
.ilmosys-align-justifyright:before {
  content: "\e91f";
}
.ilmosys-align-left:before {
  content: "\e920";
}
.ilmosys-align-right:before {
  content: "\e921";
}
.ilmosys-alpha:before {
  content: "\e922";
}
.ilmosys-ambulance:before {
  content: "\e923";
}
.ilmosys-amx:before {
  content: "\e924";
}
.ilmosys-anchor:before {
  content: "\e925";
}
.ilmosys-anchor-2:before {
  content: "\e926";
}
.ilmosys-angel:before {
  content: "\e927";
}
.ilmosys-angel-smiley:before {
  content: "\e928";
}
.ilmosys-angry:before {
  content: "\e929";
}
.ilmosys-apple:before {
  content: "\e92a";
}
.ilmosys-apple-bite:before {
  content: "\e92b";
}
.ilmosys-approved-window:before {
  content: "\e92c";
}
.ilmosys-aquarius:before {
  content: "\e92d";
}
.ilmosys-aquarius-2:before {
  content: "\e92e";
}
.ilmosys-archery:before {
  content: "\e92f";
}
.ilmosys-archery-2:before {
  content: "\e930";
}
.ilmosys-argentina:before {
  content: "\e931";
}
.ilmosys-aries:before {
  content: "\e932";
}
.ilmosys-aries-2:before {
  content: "\e933";
}
.ilmosys-army-key:before {
  content: "\e934";
}
.ilmosys-arrow-around:before {
  content: "\e935";
}
.ilmosys-arrow-back-3:before {
  content: "\e936";
}
.ilmosys-arrow-back:before {
  content: "\e937";
}
.ilmosys-arrow-back2:before {
  content: "\e938";
}
.ilmosys-arrow-barrier:before {
  content: "\e939";
}
.ilmosys-arrow-circle:before {
  content: "\e93a";
}
.ilmosys-arrow-cross:before {
  content: "\e93b";
}
.ilmosys-arrow-down:before {
  content: "\e93c";
}
.ilmosys-arrow-down2:before {
  content: "\e93d";
}
.ilmosys-arrow-down3:before {
  content: "\e93e";
}
.ilmosys-arrow-downincircle:before {
  content: "\e93f";
}
.ilmosys-arrow-fork:before {
  content: "\e940";
}
.ilmosys-arrow-forward:before {
  content: "\e941";
}
.ilmosys-arrow-forward2:before {
  content: "\e942";
}
.ilmosys-arrow-from:before {
  content: "\e943";
}
.ilmosys-arrow-inside:before {
  content: "\e944";
}
.ilmosys-arrow-inside45:before {
  content: "\e945";
}
.ilmosys-arrow-insidegap:before {
  content: "\e946";
}
.ilmosys-arrow-insidegap45:before {
  content: "\e947";
}
.ilmosys-arrow-into:before {
  content: "\e948";
}
.ilmosys-arrow-join:before {
  content: "\e949";
}
.ilmosys-arrow-junction:before {
  content: "\e94a";
}
.ilmosys-arrow-left:before {
  content: "\e94b";
}
.ilmosys-arrow-left2:before {
  content: "\e94c";
}
.ilmosys-arrow-leftincircle:before {
  content: "\e94d";
}
.ilmosys-arrow-loop:before {
  content: "\e94e";
}
.ilmosys-arrow-merge:before {
  content: "\e94f";
}
.ilmosys-arrow-mix:before {
  content: "\e950";
}
.ilmosys-arrow-next:before {
  content: "\e951";
}
.ilmosys-arrow-outleft:before {
  content: "\e952";
}
.ilmosys-arrow-outright:before {
  content: "\e953";
}
.ilmosys-arrow-outside:before {
  content: "\e954";
}
.ilmosys-arrow-outside45:before {
  content: "\e955";
}
.ilmosys-arrow-outsidegap:before {
  content: "\e956";
}
.ilmosys-arrow-outsidegap45:before {
  content: "\e957";
}
.ilmosys-arrow-over:before {
  content: "\e958";
}
.ilmosys-arrow-refresh:before {
  content: "\e959";
}
.ilmosys-arrow-refresh2:before {
  content: "\e95a";
}
.ilmosys-arrow-right:before {
  content: "\e95b";
}
.ilmosys-arrow-right2:before {
  content: "\e95c";
}
.ilmosys-arrow-rightincircle:before {
  content: "\e95d";
}
.ilmosys-arrow-shuffle:before {
  content: "\e95e";
}
.ilmosys-arrow-squiggly:before {
  content: "\e95f";
}
.ilmosys-arrow-through:before {
  content: "\e960";
}
.ilmosys-arrow-to:before {
  content: "\e961";
}
.ilmosys-arrow-turnleft:before {
  content: "\e962";
}
.ilmosys-arrow-turnright:before {
  content: "\e963";
}
.ilmosys-arrow-up:before {
  content: "\e964";
}
.ilmosys-arrow-up2:before {
  content: "\e965";
}
.ilmosys-arrow-up3:before {
  content: "\e966";
}
.ilmosys-arrow-upincircle:before {
  content: "\e967";
}
.ilmosys-arrow-xleft:before {
  content: "\e968";
}
.ilmosys-arrow-xright:before {
  content: "\e969";
}
.ilmosys-assistant:before {
  content: "\e96a";
}
.ilmosys-astronaut:before {
  content: "\e96b";
}
.ilmosys-atm:before {
  content: "\e96c";
}
.ilmosys-atom:before {
  content: "\e96d";
}
.ilmosys-at-sign:before {
  content: "\e96e";
}
.ilmosys-audio:before {
  content: "\e96f";
}
.ilmosys-auto-flash:before {
  content: "\e970";
}
.ilmosys-autumn:before {
  content: "\e971";
}
.ilmosys-a-z:before {
  content: "\e972";
}
.ilmosys-baby:before {
  content: "\e973";
}
.ilmosys-baby-clothes:before {
  content: "\e974";
}
.ilmosys-baby-clothes2:before {
  content: "\e975";
}
.ilmosys-baby-cry:before {
  content: "\e976";
}
.ilmosys-back:before {
  content: "\e977";
}
.ilmosys-background:before {
  content: "\e978";
}
.ilmosys-back-media:before {
  content: "\e979";
}
.ilmosys-back-music:before {
  content: "\e97a";
}
.ilmosys-bacteria:before {
  content: "\e97b";
}
.ilmosys-bag:before {
  content: "\e97c";
}
.ilmosys-bag-coins:before {
  content: "\e97d";
}
.ilmosys-bag-items:before {
  content: "\e97e";
}
.ilmosys-bag-quantity:before {
  content: "\e97f";
}
.ilmosys-bakelite:before {
  content: "\e980";
}
.ilmosys-ballet-shoes:before {
  content: "\e981";
}
.ilmosys-balloon:before {
  content: "\e982";
}
.ilmosys-banana:before {
  content: "\e983";
}
.ilmosys-band-aid:before {
  content: "\e984";
}
.ilmosys-bank:before {
  content: "\e985";
}
.ilmosys-bar-chart:before {
  content: "\e986";
}
.ilmosys-bar-chart2:before {
  content: "\e987";
}
.ilmosys-bar-chart3:before {
  content: "\e988";
}
.ilmosys-bar-chart4:before {
  content: "\e989";
}
.ilmosys-bar-chart5:before {
  content: "\e98a";
}
.ilmosys-bar-code:before {
  content: "\e98b";
}
.ilmosys-barricade:before {
  content: "\e98c";
}
.ilmosys-barricade-2:before {
  content: "\e98d";
}
.ilmosys-baseball:before {
  content: "\e98e";
}
.ilmosys-basket-ball:before {
  content: "\e98f";
}
.ilmosys-basket-coins:before {
  content: "\e990";
}
.ilmosys-basket-items:before {
  content: "\e991";
}
.ilmosys-basket-quantity:before {
  content: "\e992";
}
.ilmosys-bat:before {
  content: "\e993";
}
.ilmosys-bat-2:before {
  content: "\e994";
}
.ilmosys-bathrobe:before {
  content: "\e995";
}
.ilmosys-batman-mask:before {
  content: "\e996";
}
.ilmosys-battery-0:before {
  content: "\e997";
}
.ilmosys-battery-25:before {
  content: "\e998";
}
.ilmosys-battery-50:before {
  content: "\e999";
}
.ilmosys-battery-75:before {
  content: "\e99a";
}
.ilmosys-battery-100:before {
  content: "\e99b";
}
.ilmosys-battery-charge:before {
  content: "\e99c";
}
.ilmosys-bear:before {
  content: "\e99d";
}
.ilmosys-beard:before {
  content: "\e99e";
}
.ilmosys-beard-2:before {
  content: "\e99f";
}
.ilmosys-beard-3:before {
  content: "\e9a0";
}
.ilmosys-bee:before {
  content: "\e9a1";
}
.ilmosys-beer:before {
  content: "\e9a2";
}
.ilmosys-beer-glass:before {
  content: "\e9a3";
}
.ilmosys-bell:before {
  content: "\e9a4";
}
.ilmosys-bell-2:before {
  content: "\e9a5";
}
.ilmosys-belt:before {
  content: "\e9a6";
}
.ilmosys-belt-2:before {
  content: "\e9a7";
}
.ilmosys-belt-3:before {
  content: "\e9a8";
}
.ilmosys-berlin-tower:before {
  content: "\e9a9";
}
.ilmosys-beta:before {
  content: "\e9aa";
}
.ilmosys-bicycle:before {
  content: "\e9ab";
}
.ilmosys-bicycle-2:before {
  content: "\e9ac";
}
.ilmosys-bicycle-3:before {
  content: "\e9ad";
}
.ilmosys-big-bang:before {
  content: "\e9ae";
}
.ilmosys-big-data:before {
  content: "\e9af";
}
.ilmosys-bike-helmet:before {
  content: "\e9b0";
}
.ilmosys-bikini:before {
  content: "\e9b1";
}
.ilmosys-bilk-bottle2:before {
  content: "\e9b2";
}
.ilmosys-billing:before {
  content: "\e9b3";
}
.ilmosys-binocular:before {
  content: "\e9b4";
}
.ilmosys-bio-hazard:before {
  content: "\e9b5";
}
.ilmosys-biotech:before {
  content: "\e9b6";
}
.ilmosys-bird:before {
  content: "\e9b7";
}
.ilmosys-bird-deliveringletter:before {
  content: "\e9b8";
}
.ilmosys-birthday-cake:before {
  content: "\e9b9";
}
.ilmosys-bisexual:before {
  content: "\e9ba";
}
.ilmosys-bishop:before {
  content: "\e9bb";
}
.ilmosys-bitcoin:before {
  content: "\e9bc";
}
.ilmosys-blackboard:before {
  content: "\e9bd";
}
.ilmosys-black-cat:before {
  content: "\e9be";
}
.ilmosys-block-cloud:before {
  content: "\e9bf";
}
.ilmosys-block-window:before {
  content: "\e9c0";
}
.ilmosys-blood:before {
  content: "\e9c1";
}
.ilmosys-blouse:before {
  content: "\e9c2";
}
.ilmosys-blueprint:before {
  content: "\e9c3";
}
.ilmosys-board:before {
  content: "\e9c4";
}
.ilmosys-bodybuilding:before {
  content: "\e9c5";
}
.ilmosys-bold-text:before {
  content: "\e9c6";
}
.ilmosys-bone:before {
  content: "\e9c7";
}
.ilmosys-bones:before {
  content: "\e9c8";
}
.ilmosys-book:before {
  content: "\e9c9";
}
.ilmosys-bookmark:before {
  content: "\e9ca";
}
.ilmosys-books:before {
  content: "\e9cb";
}
.ilmosys-books-2:before {
  content: "\e9cc";
}
.ilmosys-boom:before {
  content: "\e9cd";
}
.ilmosys-boot:before {
  content: "\e9ce";
}
.ilmosys-boot-2:before {
  content: "\e9cf";
}
.ilmosys-bottom-totop:before {
  content: "\e9d0";
}
.ilmosys-bow:before {
  content: "\e9d1";
}
.ilmosys-bow-2:before {
  content: "\e9d2";
}
.ilmosys-bow-3:before {
  content: "\e9d3";
}
.ilmosys-bow-4:before {
  content: "\e9d4";
}
.ilmosys-bow-5:before {
  content: "\e9d5";
}
.ilmosys-bow-6:before {
  content: "\e9d6";
}
.ilmosys-bowling:before {
  content: "\e9d7";
}
.ilmosys-bowling-2:before {
  content: "\e9d8";
}
.ilmosys-box:before {
  content: "\e9d9";
}
.ilmosys-box-close:before {
  content: "\e9da";
}
.ilmosys-box-full:before {
  content: "\e9db";
}
.ilmosys-box-open:before {
  content: "\e9dc";
}
.ilmosys-box-withfolders:before {
  content: "\e9dd";
}
.ilmosys-boy:before {
  content: "\e9de";
}
.ilmosys-bra:before {
  content: "\e9df";
}
.ilmosys-brain:before {
  content: "\e9e0";
}
.ilmosys-brain-2:before {
  content: "\e9e1";
}
.ilmosys-brain-3:before {
  content: "\e9e2";
}
.ilmosys-brazil:before {
  content: "\e9e3";
}
.ilmosys-bread:before {
  content: "\e9e4";
}
.ilmosys-bread-2:before {
  content: "\e9e5";
}
.ilmosys-bridge:before {
  content: "\e9e6";
}
.ilmosys-broke-link2:before {
  content: "\e9e7";
}
.ilmosys-broken-link:before {
  content: "\e9e8";
}
.ilmosys-broom:before {
  content: "\e9e9";
}
.ilmosys-brush:before {
  content: "\e9ea";
}
.ilmosys-bucket:before {
  content: "\e9eb";
}
.ilmosys-bug:before {
  content: "\e9ec";
}
.ilmosys-building:before {
  content: "\e9ed";
}
.ilmosys-bulleted-list:before {
  content: "\e9ee";
}
.ilmosys-bus:before {
  content: "\e9ef";
}
.ilmosys-bus-2:before {
  content: "\e9f0";
}
.ilmosys-business-man:before {
  content: "\e9f1";
}
.ilmosys-business-manwoman:before {
  content: "\e9f2";
}
.ilmosys-business-mens:before {
  content: "\e9f3";
}
.ilmosys-business-woman:before {
  content: "\e9f4";
}
.ilmosys-butterfly:before {
  content: "\e9f5";
}
.ilmosys-button:before {
  content: "\e9f6";
}
.ilmosys-cable-car:before {
  content: "\e9f7";
}
.ilmosys-cake:before {
  content: "\e9f8";
}
.ilmosys-calculator:before {
  content: "\e9f9";
}
.ilmosys-calculator-2:before {
  content: "\e9fa";
}
.ilmosys-calculator-3:before {
  content: "\e9fb";
}
.ilmosys-calendar:before {
  content: "\e9fc";
}
.ilmosys-calendar-2:before {
  content: "\e9fd";
}
.ilmosys-calendar-3:before {
  content: "\e9fe";
}
.ilmosys-calendar-4:before {
  content: "\e9ff";
}
.ilmosys-calendar-clock:before {
  content: "\ea00";
}
.ilmosys-camel:before {
  content: "\ea01";
}
.ilmosys-camera:before {
  content: "\ea02";
}
.ilmosys-camera-2:before {
  content: "\ea03";
}
.ilmosys-camera-3:before {
  content: "\ea04";
}
.ilmosys-camera-4:before {
  content: "\ea05";
}
.ilmosys-camera-5:before {
  content: "\ea06";
}
.ilmosys-camera-back:before {
  content: "\ea07";
}
.ilmosys-can:before {
  content: "\ea08";
}
.ilmosys-can-2:before {
  content: "\ea09";
}
.ilmosys-canada:before {
  content: "\ea0a";
}
.ilmosys-cancer:before {
  content: "\ea0b";
}
.ilmosys-cancer-2:before {
  content: "\ea0c";
}
.ilmosys-cancer-3:before {
  content: "\ea0d";
}
.ilmosys-candle:before {
  content: "\ea0e";
}
.ilmosys-candy:before {
  content: "\ea0f";
}
.ilmosys-candy-cane:before {
  content: "\ea10";
}
.ilmosys-cannon:before {
  content: "\ea11";
}
.ilmosys-cap:before {
  content: "\ea12";
}
.ilmosys-cap-2:before {
  content: "\ea13";
}
.ilmosys-cap-3:before {
  content: "\ea14";
}
.ilmosys-capricorn:before {
  content: "\ea15";
}
.ilmosys-capricorn-2:before {
  content: "\ea16";
}
.ilmosys-cap-smiley:before {
  content: "\ea17";
}
.ilmosys-car:before {
  content: "\ea18";
}
.ilmosys-car-2:before {
  content: "\ea19";
}
.ilmosys-car-3:before {
  content: "\ea1a";
}
.ilmosys-car-coins:before {
  content: "\ea1b";
}
.ilmosys-cardigan:before {
  content: "\ea1c";
}
.ilmosys-cardiovascular:before {
  content: "\ea1d";
}
.ilmosys-car-items:before {
  content: "\ea1e";
}
.ilmosys-cart-quantity:before {
  content: "\ea1f";
}
.ilmosys-car-wheel:before {
  content: "\ea20";
}
.ilmosys-casette-tape:before {
  content: "\ea21";
}
.ilmosys-cash-register:before {
  content: "\ea22";
}
.ilmosys-cash-register2:before {
  content: "\ea23";
}
.ilmosys-castle:before {
  content: "\ea24";
}
.ilmosys-cat:before {
  content: "\ea25";
}
.ilmosys-cathedral:before {
  content: "\ea26";
}
.ilmosys-cauldron:before {
  content: "\ea27";
}
.ilmosys-cd:before {
  content: "\ea28";
}
.ilmosys-cd-2:before {
  content: "\ea29";
}
.ilmosys-cd-cover:before {
  content: "\ea2a";
}
.ilmosys-cello:before {
  content: "\ea2b";
}
.ilmosys-celsius:before {
  content: "\ea2c";
}
.ilmosys-chacked-flag:before {
  content: "\ea2d";
}
.ilmosys-chair:before {
  content: "\ea2e";
}
.ilmosys-charger:before {
  content: "\ea2f";
}
.ilmosys-check:before {
  content: "\ea30";
}
.ilmosys-check-2:before {
  content: "\ea31";
}
.ilmosys-checked-user:before {
  content: "\ea32";
}
.ilmosys-checkmate:before {
  content: "\ea33";
}
.ilmosys-checkout:before {
  content: "\ea34";
}
.ilmosys-checkout-bag:before {
  content: "\ea35";
}
.ilmosys-checkout-basket:before {
  content: "\ea36";
}
.ilmosys-cheese:before {
  content: "\ea37";
}
.ilmosys-cheetah:before {
  content: "\ea38";
}
.ilmosys-chef:before {
  content: "\ea39";
}
.ilmosys-chef-hat:before {
  content: "\ea3a";
}
.ilmosys-chef-hat2:before {
  content: "\ea3b";
}
.ilmosys-chemical:before {
  content: "\ea3c";
}
.ilmosys-chemical-2:before {
  content: "\ea3d";
}
.ilmosys-chemical-3:before {
  content: "\ea3e";
}
.ilmosys-chemical-4:before {
  content: "\ea3f";
}
.ilmosys-chemical-5:before {
  content: "\ea40";
}
.ilmosys-chess:before {
  content: "\ea41";
}
.ilmosys-chess-board:before {
  content: "\ea42";
}
.ilmosys-chicken:before {
  content: "\ea43";
}
.ilmosys-chile:before {
  content: "\ea44";
}
.ilmosys-chimney:before {
  content: "\ea45";
}
.ilmosys-china:before {
  content: "\ea46";
}
.ilmosys-chinese-temple:before {
  content: "\ea47";
}
.ilmosys-chip:before {
  content: "\ea48";
}
.ilmosys-chopsticks:before {
  content: "\ea49";
}
.ilmosys-chopsticks-2:before {
  content: "\ea4a";
}
.ilmosys-christmas:before {
  content: "\ea4b";
}
.ilmosys-christmas-ball:before {
  content: "\ea4c";
}
.ilmosys-christmas-bell:before {
  content: "\ea4d";
}
.ilmosys-christmas-candle:before {
  content: "\ea4e";
}
.ilmosys-christmas-hat:before {
  content: "\ea4f";
}
.ilmosys-christmas-sleigh:before {
  content: "\ea50";
}
.ilmosys-christmas-snowman:before {
  content: "\ea51";
}
.ilmosys-christmas-sock:before {
  content: "\ea52";
}
.ilmosys-christmas-tree:before {
  content: "\ea53";
}
.ilmosys-chrysler-building:before {
  content: "\ea54";
}
.ilmosys-cinema:before {
  content: "\ea55";
}
.ilmosys-circular-point:before {
  content: "\ea56";
}
.ilmosys-city-hall:before {
  content: "\ea57";
}
.ilmosys-clamp:before {
  content: "\ea58";
}
.ilmosys-clapperboard-close:before {
  content: "\ea59";
}
.ilmosys-clapperboard-open:before {
  content: "\ea5a";
}
.ilmosys-claps:before {
  content: "\ea5b";
}
.ilmosys-clef:before {
  content: "\ea5c";
}
.ilmosys-clinic:before {
  content: "\ea5d";
}
.ilmosys-clock:before {
  content: "\ea5e";
}
.ilmosys-clock-2:before {
  content: "\ea5f";
}
.ilmosys-clock-3:before {
  content: "\ea60";
}
.ilmosys-clock-4:before {
  content: "\ea61";
}
.ilmosys-clock-back:before {
  content: "\ea62";
}
.ilmosys-clock-forward:before {
  content: "\ea63";
}
.ilmosys-close:before {
  content: "\ea64";
}
.ilmosys-close-window:before {
  content: "\ea65";
}
.ilmosys-clothing-store:before {
  content: "\ea66";
}
.ilmosys-cloud:before {
  content: "\ea67";
}
.ilmosys-cloud2:before {
  content: "\ea68";
}
.ilmosys-cloud3:before {
  content: "\ea69";
}
.ilmosys-cloud-camera:before {
  content: "\ea6a";
}
.ilmosys-cloud-computer:before {
  content: "\ea6b";
}
.ilmosys-cloud-email:before {
  content: "\ea6c";
}
.ilmosys-cloud-hail:before {
  content: "\ea6d";
}
.ilmosys-cloud-laptop:before {
  content: "\ea6e";
}
.ilmosys-cloud-lock:before {
  content: "\ea6f";
}
.ilmosys-cloud-moon:before {
  content: "\ea70";
}
.ilmosys-cloud-music:before {
  content: "\ea71";
}
.ilmosys-cloud-picture:before {
  content: "\ea72";
}
.ilmosys-cloud-rain:before {
  content: "\ea73";
}
.ilmosys-cloud-remove:before {
  content: "\ea74";
}
.ilmosys-clouds:before {
  content: "\ea75";
}
.ilmosys-cloud-secure:before {
  content: "\ea76";
}
.ilmosys-cloud-settings:before {
  content: "\ea77";
}
.ilmosys-cloud-smartphone:before {
  content: "\ea78";
}
.ilmosys-cloud-snow:before {
  content: "\ea79";
}
.ilmosys-cloud-sun:before {
  content: "\ea7a";
}
.ilmosys-clouds-weather:before {
  content: "\ea7b";
}
.ilmosys-cloud-tablet:before {
  content: "\ea7c";
}
.ilmosys-cloud-video:before {
  content: "\ea7d";
}
.ilmosys-cloud-weather:before {
  content: "\ea7e";
}
.ilmosys-clown:before {
  content: "\ea7f";
}
.ilmosys-cmyk:before {
  content: "\ea80";
}
.ilmosys-coat:before {
  content: "\ea81";
}
.ilmosys-cocktail:before {
  content: "\ea82";
}
.ilmosys-coconut:before {
  content: "\ea83";
}
.ilmosys-code-window:before {
  content: "\ea84";
}
.ilmosys-coding:before {
  content: "\ea85";
}
.ilmosys-coffee:before {
  content: "\ea86";
}
.ilmosys-coffee-2:before {
  content: "\ea87";
}
.ilmosys-coffee-bean:before {
  content: "\ea88";
}
.ilmosys-coffee-machine:before {
  content: "\ea89";
}
.ilmosys-coffee-togo:before {
  content: "\ea8a";
}
.ilmosys-coffin:before {
  content: "\ea8b";
}
.ilmosys-coin:before {
  content: "\ea8c";
}
.ilmosys-coins:before {
  content: "\ea8d";
}
.ilmosys-coins-2:before {
  content: "\ea8e";
}
.ilmosys-coins-3:before {
  content: "\ea8f";
}
.ilmosys-colombia:before {
  content: "\ea90";
}
.ilmosys-colosseum:before {
  content: "\ea91";
}
.ilmosys-column:before {
  content: "\ea92";
}
.ilmosys-column-2:before {
  content: "\ea93";
}
.ilmosys-column-3:before {
  content: "\ea94";
}
.ilmosys-comb:before {
  content: "\ea95";
}
.ilmosys-comb-2:before {
  content: "\ea96";
}
.ilmosys-communication-tower:before {
  content: "\ea97";
}
.ilmosys-communication-tower2:before {
  content: "\ea98";
}
.ilmosys-compass:before {
  content: "\ea99";
}
.ilmosys-compass-2:before {
  content: "\ea9a";
}
.ilmosys-compass-3:before {
  content: "\ea9b";
}
.ilmosys-compass-4:before {
  content: "\ea9c";
}
.ilmosys-compass-rose:before {
  content: "\ea9d";
}
.ilmosys-computer:before {
  content: "\ea9e";
}
.ilmosys-computer-2:before {
  content: "\ea9f";
}
.ilmosys-computer-3:before {
  content: "\eaa0";
}
.ilmosys-computer-secure:before {
  content: "\eaa1";
}
.ilmosys-conference:before {
  content: "\eaa2";
}
.ilmosys-confused:before {
  content: "\eaa3";
}
.ilmosys-conservation:before {
  content: "\eaa4";
}
.ilmosys-consulting:before {
  content: "\eaa5";
}
.ilmosys-contrast:before {
  content: "\eaa6";
}
.ilmosys-control:before {
  content: "\eaa7";
}
.ilmosys-control-2:before {
  content: "\eaa8";
}
.ilmosys-cookie-man:before {
  content: "\eaa9";
}
.ilmosys-cookies:before {
  content: "\eaaa";
}
.ilmosys-cool:before {
  content: "\eaab";
}
.ilmosys-cool-guy:before {
  content: "\eaac";
}
.ilmosys-copyright:before {
  content: "\eaad";
}
.ilmosys-costume:before {
  content: "\eaae";
}
.ilmosys-couple-sign:before {
  content: "\eaaf";
}
.ilmosys-cow:before {
  content: "\eab0";
}
.ilmosys-cpu:before {
  content: "\eab1";
}
.ilmosys-crane:before {
  content: "\eab2";
}
.ilmosys-cranium:before {
  content: "\eab3";
}
.ilmosys-credit-card:before {
  content: "\eab4";
}
.ilmosys-credit-card2:before {
  content: "\eab5";
}
.ilmosys-credit-card3:before {
  content: "\eab6";
}
.ilmosys-cricket:before {
  content: "\eab7";
}
.ilmosys-criminal:before {
  content: "\eab8";
}
.ilmosys-croissant:before {
  content: "\eab9";
}
.ilmosys-crop-2:before {
  content: "\eaba";
}
.ilmosys-crop-3:before {
  content: "\eabb";
}
.ilmosys-crown:before {
  content: "\eabc";
}
.ilmosys-crown-2:before {
  content: "\eabd";
}
.ilmosys-crying:before {
  content: "\eabe";
}
.ilmosys-cube-molecule:before {
  content: "\eabf";
}
.ilmosys-cube-molecule2:before {
  content: "\eac0";
}
.ilmosys-cupcake:before {
  content: "\eac1";
}
.ilmosys-cursor:before {
  content: "\eac2";
}
.ilmosys-cursor-click:before {
  content: "\eac3";
}
.ilmosys-cursor-click2:before {
  content: "\eac4";
}
.ilmosys-cursor-move:before {
  content: "\eac5";
}
.ilmosys-cursor-move2:before {
  content: "\eac6";
}
.ilmosys-cursor-select:before {
  content: "\eac7";
}
.ilmosys-dam:before {
  content: "\eac8";
}
.ilmosys-danemark:before {
  content: "\eac9";
}
.ilmosys-danger:before {
  content: "\eaca";
}
.ilmosys-danger-2:before {
  content: "\eacb";
}
.ilmosys-dashboard:before {
  content: "\eacc";
}
.ilmosys-data:before {
  content: "\eacd";
}
.ilmosys-data-backup:before {
  content: "\eace";
}
.ilmosys-data-block:before {
  content: "\eacf";
}
.ilmosys-data-center:before {
  content: "\ead0";
}
.ilmosys-data-clock:before {
  content: "\ead1";
}
.ilmosys-data-cloud:before {
  content: "\ead2";
}
.ilmosys-data-compress:before {
  content: "\ead3";
}
.ilmosys-data-copy:before {
  content: "\ead4";
}
.ilmosys-data-download:before {
  content: "\ead5";
}
.ilmosys-data-financial:before {
  content: "\ead6";
}
.ilmosys-data-key:before {
  content: "\ead7";
}
.ilmosys-data-lock:before {
  content: "\ead8";
}
.ilmosys-data-network:before {
  content: "\ead9";
}
.ilmosys-data-password:before {
  content: "\eada";
}
.ilmosys-data-power:before {
  content: "\eadb";
}
.ilmosys-data-refresh:before {
  content: "\eadc";
}
.ilmosys-data-save:before {
  content: "\eadd";
}
.ilmosys-data-search:before {
  content: "\eade";
}
.ilmosys-data-security:before {
  content: "\eadf";
}
.ilmosys-data-settings:before {
  content: "\eae0";
}
.ilmosys-data-sharing:before {
  content: "\eae1";
}
.ilmosys-data-shield:before {
  content: "\eae2";
}
.ilmosys-data-signal:before {
  content: "\eae3";
}
.ilmosys-data-storage:before {
  content: "\eae4";
}
.ilmosys-data-stream:before {
  content: "\eae5";
}
.ilmosys-data-transfer:before {
  content: "\eae6";
}
.ilmosys-data-unlock:before {
  content: "\eae7";
}
.ilmosys-data-upload:before {
  content: "\eae8";
}
.ilmosys-data-yes:before {
  content: "\eae9";
}
.ilmosys-david-star:before {
  content: "\eaea";
}
.ilmosys-daylight:before {
  content: "\eaeb";
}
.ilmosys-death:before {
  content: "\eaec";
}
.ilmosys-dec:before {
  content: "\eaed";
}
.ilmosys-decrase-inedit:before {
  content: "\eaee";
}
.ilmosys-deer:before {
  content: "\eaef";
}
.ilmosys-deer-2:before {
  content: "\eaf0";
}
.ilmosys-delete-file:before {
  content: "\eaf1";
}
.ilmosys-delete-window:before {
  content: "\eaf2";
}
.ilmosys-depression:before {
  content: "\eaf3";
}
.ilmosys-device-syncwithcloud:before {
  content: "\eaf4";
}
.ilmosys-d-eyeglasses:before {
  content: "\eaf5";
}
.ilmosys-d-eyeglasses2:before {
  content: "\eaf6";
}
.ilmosys-diamond:before {
  content: "\eaf7";
}
.ilmosys-dice:before {
  content: "\eaf8";
}
.ilmosys-dice-2:before {
  content: "\eaf9";
}
.ilmosys-digital-drawing:before {
  content: "\eafa";
}
.ilmosys-dinosaur:before {
  content: "\eafb";
}
.ilmosys-diploma:before {
  content: "\eafc";
}
.ilmosys-diploma-2:before {
  content: "\eafd";
}
.ilmosys-direction-east:before {
  content: "\eafe";
}
.ilmosys-direction-north:before {
  content: "\eaff";
}
.ilmosys-direction-south:before {
  content: "\eb00";
}
.ilmosys-direction-west:before {
  content: "\eb01";
}
.ilmosys-director:before {
  content: "\eb02";
}
.ilmosys-disk:before {
  content: "\eb03";
}
.ilmosys-dj:before {
  content: "\eb04";
}
.ilmosys-dna:before {
  content: "\eb05";
}
.ilmosys-dna-2:before {
  content: "\eb06";
}
.ilmosys-dna-helix:before {
  content: "\eb07";
}
.ilmosys-doctor:before {
  content: "\eb08";
}
.ilmosys-dog:before {
  content: "\eb09";
}
.ilmosys-dollar:before {
  content: "\eb0a";
}
.ilmosys-dollar-sign:before {
  content: "\eb0b";
}
.ilmosys-dollar-sign2:before {
  content: "\eb0c";
}
.ilmosys-dolphin:before {
  content: "\eb0d";
}
.ilmosys-domino:before {
  content: "\eb0e";
}
.ilmosys-door:before {
  content: "\eb0f";
}
.ilmosys-door-hanger:before {
  content: "\eb10";
}
.ilmosys-double-circle:before {
  content: "\eb11";
}
.ilmosys-double-tap:before {
  content: "\eb12";
}
.ilmosys-doughnut:before {
  content: "\eb13";
}
.ilmosys-dove:before {
  content: "\eb14";
}
.ilmosys-down:before {
  content: "\eb15";
}
.ilmosys-down-2:before {
  content: "\eb16";
}
.ilmosys-down-3:before {
  content: "\eb17";
}
.ilmosys-down-4:before {
  content: "\eb18";
}
.ilmosys-download:before {
  content: "\eb19";
}
.ilmosys-download-2:before {
  content: "\eb1a";
}
.ilmosys-download-fromcloud:before {
  content: "\eb1b";
}
.ilmosys-download-window:before {
  content: "\eb1c";
}
.ilmosys-downward:before {
  content: "\eb1d";
}
.ilmosys-drag:before {
  content: "\eb1e";
}
.ilmosys-drag-down:before {
  content: "\eb1f";
}
.ilmosys-drag-left:before {
  content: "\eb20";
}
.ilmosys-drag-right:before {
  content: "\eb21";
}
.ilmosys-drag-up:before {
  content: "\eb22";
}
.ilmosys-dress:before {
  content: "\eb23";
}
.ilmosys-drill:before {
  content: "\eb24";
}
.ilmosys-drill-2:before {
  content: "\eb25";
}
.ilmosys-drop:before {
  content: "\eb26";
}
.ilmosys-drum:before {
  content: "\eb27";
}
.ilmosys-dry:before {
  content: "\eb28";
}
.ilmosys-duck:before {
  content: "\eb29";
}
.ilmosys-dumbbell:before {
  content: "\eb2a";
}
.ilmosys-duplicate-layer:before {
  content: "\eb2b";
}
.ilmosys-duplicate-window:before {
  content: "\eb2c";
}
.ilmosys-dvd:before {
  content: "\eb2d";
}
.ilmosys-eagle:before {
  content: "\eb2e";
}
.ilmosys-ear:before {
  content: "\eb2f";
}
.ilmosys-earphones:before {
  content: "\eb30";
}
.ilmosys-earphones-2:before {
  content: "\eb31";
}
.ilmosys-eci-icon:before {
  content: "\eb32";
}
.ilmosys-edit:before {
  content: "\eb33";
}
.ilmosys-edit-map:before {
  content: "\eb34";
}
.ilmosys-eggs:before {
  content: "\eb35";
}
.ilmosys-egypt:before {
  content: "\eb36";
}
.ilmosys-eifel-tower:before {
  content: "\eb37";
}
.ilmosys-eject:before {
  content: "\eb38";
}
.ilmosys-eject-2:before {
  content: "\eb39";
}
.ilmosys-elbow:before {
  content: "\eb3a";
}
.ilmosys-el-castillo:before {
  content: "\eb3b";
}
.ilmosys-electric-guitar:before {
  content: "\eb3c";
}
.ilmosys-electricity:before {
  content: "\eb3d";
}
.ilmosys-elephant:before {
  content: "\eb3e";
}
.ilmosys-embassy:before {
  content: "\eb3f";
}
.ilmosys-empire-statebuilding:before {
  content: "\eb40";
}
.ilmosys-empty-box:before {
  content: "\eb41";
}
.ilmosys-end:before {
  content: "\eb42";
}
.ilmosys-end-2:before {
  content: "\eb43";
}
.ilmosys-endways:before {
  content: "\eb44";
}
.ilmosys-engineering:before {
  content: "\eb45";
}
.ilmosys-envelope:before {
  content: "\eb46";
}
.ilmosys-envelope-2:before {
  content: "\eb47";
}
.ilmosys-environmental:before {
  content: "\eb48";
}
.ilmosys-environmental-2:before {
  content: "\eb49";
}
.ilmosys-environmental-3:before {
  content: "\eb4a";
}
.ilmosys-equalizer:before {
  content: "\eb4b";
}
.ilmosys-eraser:before {
  content: "\eb4c";
}
.ilmosys-eraser-2:before {
  content: "\eb4d";
}
.ilmosys-eraser-3:before {
  content: "\eb4e";
}
.ilmosys-error-404window:before {
  content: "\eb4f";
}
.ilmosys-euro:before {
  content: "\eb50";
}
.ilmosys-euro-sign:before {
  content: "\eb51";
}
.ilmosys-euro-sign2:before {
  content: "\eb52";
}
.ilmosys-evil:before {
  content: "\eb53";
}
.ilmosys-explode:before {
  content: "\eb54";
}
.ilmosys-eye:before {
  content: "\eb55";
}
.ilmosys-eye-2:before {
  content: "\eb56";
}
.ilmosys-eye-blind:before {
  content: "\eb57";
}
.ilmosys-eyebrow:before {
  content: "\eb58";
}
.ilmosys-eyebrow-2:before {
  content: "\eb59";
}
.ilmosys-eyebrow-3:before {
  content: "\eb5a";
}
.ilmosys-eyeglasses-smiley:before {
  content: "\eb5b";
}
.ilmosys-eyeglasses-smiley2:before {
  content: "\eb5c";
}
.ilmosys-eye-invisible:before {
  content: "\eb5d";
}
.ilmosys-eye-scan:before {
  content: "\eb5e";
}
.ilmosys-eye-visible:before {
  content: "\eb5f";
}
.ilmosys-face-style:before {
  content: "\eb60";
}
.ilmosys-face-style2:before {
  content: "\eb61";
}
.ilmosys-face-style3:before {
  content: "\eb62";
}
.ilmosys-face-style4:before {
  content: "\eb63";
}
.ilmosys-face-style5:before {
  content: "\eb64";
}
.ilmosys-face-style6:before {
  content: "\eb65";
}
.ilmosys-factory:before {
  content: "\eb66";
}
.ilmosys-factory-2:before {
  content: "\eb67";
}
.ilmosys-fahrenheit:before {
  content: "\eb68";
}
.ilmosys-family-sign:before {
  content: "\eb69";
}
.ilmosys-fan:before {
  content: "\eb6a";
}
.ilmosys-farmer:before {
  content: "\eb6b";
}
.ilmosys-fashion:before {
  content: "\eb6c";
}
.ilmosys-favorite-window:before {
  content: "\eb6d";
}
.ilmosys-fax:before {
  content: "\eb6e";
}
.ilmosys-feather:before {
  content: "\eb6f";
}
.ilmosys-female:before {
  content: "\eb70";
}
.ilmosys-female-2:before {
  content: "\eb71";
}
.ilmosys-female-sign:before {
  content: "\eb72";
}
.ilmosys-file:before {
  content: "\eb73";
}
.ilmosys-file-block:before {
  content: "\eb74";
}
.ilmosys-file-bookmark:before {
  content: "\eb75";
}
.ilmosys-file-chart:before {
  content: "\eb76";
}
.ilmosys-file-clipboard:before {
  content: "\eb77";
}
.ilmosys-file-clipboardfiletext:before {
  content: "\eb78";
}
.ilmosys-file-clipboardtextimage:before {
  content: "\eb79";
}
.ilmosys-file-cloud:before {
  content: "\eb7a";
}
.ilmosys-file-copy:before {
  content: "\eb7b";
}
.ilmosys-file-copy2:before {
  content: "\eb7c";
}
.ilmosys-file-csv:before {
  content: "\eb7d";
}
.ilmosys-file-download:before {
  content: "\eb7e";
}
.ilmosys-file-edit:before {
  content: "\eb7f";
}
.ilmosys-file-excel:before {
  content: "\eb80";
}
.ilmosys-file-favorite:before {
  content: "\eb81";
}
.ilmosys-file-fire:before {
  content: "\eb82";
}
.ilmosys-file-graph:before {
  content: "\eb83";
}
.ilmosys-file-hide:before {
  content: "\eb84";
}
.ilmosys-file-horizontal:before {
  content: "\eb85";
}
.ilmosys-file-horizontaltext:before {
  content: "\eb86";
}
.ilmosys-file-html:before {
  content: "\eb87";
}
.ilmosys-file-jpg:before {
  content: "\eb88";
}
.ilmosys-file-link:before {
  content: "\eb89";
}
.ilmosys-file-loading:before {
  content: "\eb8a";
}
.ilmosys-file-lock:before {
  content: "\eb8b";
}
.ilmosys-file-love:before {
  content: "\eb8c";
}
.ilmosys-file-music:before {
  content: "\eb8d";
}
.ilmosys-file-network:before {
  content: "\eb8e";
}
.ilmosys-file-pictures:before {
  content: "\eb8f";
}
.ilmosys-file-pie:before {
  content: "\eb90";
}
.ilmosys-file-presentation:before {
  content: "\eb91";
}
.ilmosys-file-refresh:before {
  content: "\eb92";
}
.ilmosys-files:before {
  content: "\eb93";
}
.ilmosys-file-search:before {
  content: "\eb94";
}
.ilmosys-file-settings:before {
  content: "\eb95";
}
.ilmosys-file-share:before {
  content: "\eb96";
}
.ilmosys-file-textimage:before {
  content: "\eb97";
}
.ilmosys-file-trash:before {
  content: "\eb98";
}
.ilmosys-file-txt:before {
  content: "\eb99";
}
.ilmosys-file-upload:before {
  content: "\eb9a";
}
.ilmosys-file-video:before {
  content: "\eb9b";
}
.ilmosys-file-word:before {
  content: "\eb9c";
}
.ilmosys-file-zip:before {
  content: "\eb9d";
}
.ilmosys-film:before {
  content: "\eb9e";
}
.ilmosys-film-board:before {
  content: "\eb9f";
}
.ilmosys-film-cartridge:before {
  content: "\eba0";
}
.ilmosys-film-strip:before {
  content: "\eba1";
}
.ilmosys-film-video:before {
  content: "\eba2";
}
.ilmosys-filter:before {
  content: "\eba3";
}
.ilmosys-filter-2:before {
  content: "\eba4";
}
.ilmosys-financial:before {
  content: "\eba5";
}
.ilmosys-find-user:before {
  content: "\eba6";
}
.ilmosys-finger:before {
  content: "\eba7";
}
.ilmosys-finger-dragfoursides:before {
  content: "\eba8";
}
.ilmosys-finger-dragtwosides:before {
  content: "\eba9";
}
.ilmosys-fingerprint:before {
  content: "\ebaa";
}
.ilmosys-finger-print:before {
  content: "\ebab";
}
.ilmosys-fingerprint-2:before {
  content: "\ebac";
}
.ilmosys-fire-flame:before {
  content: "\ebad";
}
.ilmosys-fire-flame2:before {
  content: "\ebae";
}
.ilmosys-fire-hydrant:before {
  content: "\ebaf";
}
.ilmosys-fire-staion:before {
  content: "\ebb0";
}
.ilmosys-firewall:before {
  content: "\ebb1";
}
.ilmosys-first:before {
  content: "\ebb2";
}
.ilmosys-first-aid:before {
  content: "\ebb3";
}
.ilmosys-fish:before {
  content: "\ebb4";
}
.ilmosys-fish-food:before {
  content: "\ebb5";
}
.ilmosys-fit-to:before {
  content: "\ebb6";
}
.ilmosys-fit-to2:before {
  content: "\ebb7";
}
.ilmosys-five-fingers:before {
  content: "\ebb8";
}
.ilmosys-five-fingersdrag:before {
  content: "\ebb9";
}
.ilmosys-five-fingersdrag2:before {
  content: "\ebba";
}
.ilmosys-five-fingerstouch:before {
  content: "\ebbb";
}
.ilmosys-flag:before {
  content: "\ebbc";
}
.ilmosys-flag-2:before {
  content: "\ebbd";
}
.ilmosys-flag-3:before {
  content: "\ebbe";
}
.ilmosys-flag-4:before {
  content: "\ebbf";
}
.ilmosys-flag-5:before {
  content: "\ebc0";
}
.ilmosys-flag-6:before {
  content: "\ebc1";
}
.ilmosys-flamingo:before {
  content: "\ebc2";
}
.ilmosys-flash:before {
  content: "\ebc3";
}
.ilmosys-flash-2:before {
  content: "\ebc4";
}
.ilmosys-flashlight:before {
  content: "\ebc5";
}
.ilmosys-flash-video:before {
  content: "\ebc6";
}
.ilmosys-flask:before {
  content: "\ebc7";
}
.ilmosys-flask-2:before {
  content: "\ebc8";
}
.ilmosys-flick:before {
  content: "\ebc9";
}
.ilmosys-flowerpot:before {
  content: "\ebca";
}
.ilmosys-fluorescent:before {
  content: "\ebcb";
}
.ilmosys-fog-day:before {
  content: "\ebcc";
}
.ilmosys-fog-night:before {
  content: "\ebcd";
}
.ilmosys-folder:before {
  content: "\ebce";
}
.ilmosys-folder-add:before {
  content: "\ebcf";
}
.ilmosys-folder-archive:before {
  content: "\ebd0";
}
.ilmosys-folder-binder:before {
  content: "\ebd1";
}
.ilmosys-folder-binder2:before {
  content: "\ebd2";
}
.ilmosys-folder-block:before {
  content: "\ebd3";
}
.ilmosys-folder-bookmark:before {
  content: "\ebd4";
}
.ilmosys-folder-close:before {
  content: "\ebd5";
}
.ilmosys-folder-cloud:before {
  content: "\ebd6";
}
.ilmosys-folder-delete:before {
  content: "\ebd7";
}
.ilmosys-folder-download:before {
  content: "\ebd8";
}
.ilmosys-folder-edit:before {
  content: "\ebd9";
}
.ilmosys-folder-favorite:before {
  content: "\ebda";
}
.ilmosys-folder-fire:before {
  content: "\ebdb";
}
.ilmosys-folder-hide:before {
  content: "\ebdc";
}
.ilmosys-folder-link:before {
  content: "\ebdd";
}
.ilmosys-folder-loading:before {
  content: "\ebde";
}
.ilmosys-folder-lock:before {
  content: "\ebdf";
}
.ilmosys-folder-love:before {
  content: "\ebe0";
}
.ilmosys-folder-music:before {
  content: "\ebe1";
}
.ilmosys-folder-network:before {
  content: "\ebe2";
}
.ilmosys-folder-open:before {
  content: "\ebe3";
}
.ilmosys-folder-open2:before {
  content: "\ebe4";
}
.ilmosys-folder-organizing:before {
  content: "\ebe5";
}
.ilmosys-folder-pictures:before {
  content: "\ebe6";
}
.ilmosys-folder-refresh:before {
  content: "\ebe7";
}
.ilmosys-folder-remove:before {
  content: "\ebe8";
}
.ilmosys-folders:before {
  content: "\ebe9";
}
.ilmosys-folder-search:before {
  content: "\ebea";
}
.ilmosys-folder-settings:before {
  content: "\ebeb";
}
.ilmosys-folder-share:before {
  content: "\ebec";
}
.ilmosys-folder-trash:before {
  content: "\ebed";
}
.ilmosys-folder-upload:before {
  content: "\ebee";
}
.ilmosys-folder-video:before {
  content: "\ebef";
}
.ilmosys-folder-withdocument:before {
  content: "\ebf0";
}
.ilmosys-folder-zip:before {
  content: "\ebf1";
}
.ilmosys-font-color:before {
  content: "\ebf2";
}
.ilmosys-font-name:before {
  content: "\ebf3";
}
.ilmosys-font-size:before {
  content: "\ebf4";
}
.ilmosys-font-style:before {
  content: "\ebf5";
}
.ilmosys-font-stylesubscript:before {
  content: "\ebf6";
}
.ilmosys-font-stylesuperscript:before {
  content: "\ebf7";
}
.ilmosys-font-window:before {
  content: "\ebf8";
}
.ilmosys-foot:before {
  content: "\ebf9";
}
.ilmosys-foot-2:before {
  content: "\ebfa";
}
.ilmosys-football:before {
  content: "\ebfb";
}
.ilmosys-football-2:before {
  content: "\ebfc";
}
.ilmosys-footprint:before {
  content: "\ebfd";
}
.ilmosys-footprint-2:before {
  content: "\ebfe";
}
.ilmosys-footprint-3:before {
  content: "\ebff";
}
.ilmosys-forest:before {
  content: "\ec00";
}
.ilmosys-fork:before {
  content: "\ec01";
}
.ilmosys-formula:before {
  content: "\ec02";
}
.ilmosys-forward:before {
  content: "\ec03";
}
.ilmosys-fountain-pen:before {
  content: "\ec04";
}
.ilmosys-four-fingers:before {
  content: "\ec05";
}
.ilmosys-four-fingersdrag:before {
  content: "\ec06";
}
.ilmosys-four-fingersdrag2:before {
  content: "\ec07";
}
.ilmosys-four-fingerstouch:before {
  content: "\ec08";
}
.ilmosys-fox:before {
  content: "\ec09";
}
.ilmosys-frankenstein:before {
  content: "\ec0a";
}
.ilmosys-french-fries:before {
  content: "\ec0b";
}
.ilmosys-frog:before {
  content: "\ec0c";
}
.ilmosys-fruits:before {
  content: "\ec0d";
}
.ilmosys-fuel:before {
  content: "\ec0e";
}
.ilmosys-full-bag:before {
  content: "\ec0f";
}
.ilmosys-full-basket:before {
  content: "\ec10";
}
.ilmosys-full-cart:before {
  content: "\ec11";
}
.ilmosys-full-moon:before {
  content: "\ec12";
}
.ilmosys-full-screen:before {
  content: "\ec13";
}
.ilmosys-full-screen2:before {
  content: "\ec14";
}
.ilmosys-full-view:before {
  content: "\ec15";
}
.ilmosys-full-view2:before {
  content: "\ec16";
}
.ilmosys-full-viewwindow:before {
  content: "\ec17";
}
.ilmosys-function:before {
  content: "\ec18";
}
.ilmosys-funky:before {
  content: "\ec19";
}
.ilmosys-funny-bicycle:before {
  content: "\ec1a";
}
.ilmosys-gamepad:before {
  content: "\ec1b";
}
.ilmosys-gamepad-2:before {
  content: "\ec1c";
}
.ilmosys-gas-pump:before {
  content: "\ec1d";
}
.ilmosys-gaugage:before {
  content: "\ec1e";
}
.ilmosys-gaugage-2:before {
  content: "\ec1f";
}
.ilmosys-gay:before {
  content: "\ec20";
}
.ilmosys-gear:before {
  content: "\ec21";
}
.ilmosys-gear-2:before {
  content: "\ec22";
}
.ilmosys-gears:before {
  content: "\ec23";
}
.ilmosys-gears-2:before {
  content: "\ec24";
}
.ilmosys-geek:before {
  content: "\ec25";
}
.ilmosys-geek-2:before {
  content: "\ec26";
}
.ilmosys-gemini:before {
  content: "\ec27";
}
.ilmosys-gemini-2:before {
  content: "\ec28";
}
.ilmosys-genius:before {
  content: "\ec29";
}
.ilmosys-gentleman:before {
  content: "\ec2a";
}
.ilmosys-geo:before {
  content: "\ec2b";
}
.ilmosys-geo2:before {
  content: "\ec2c";
}
.ilmosys-geo3:before {
  content: "\ec2d";
}
.ilmosys-geo22:before {
  content: "\ec2e";
}
.ilmosys-geo23:before {
  content: "\ec2f";
}
.ilmosys-geo24:before {
  content: "\ec30";
}
.ilmosys-geo2-close:before {
  content: "\ec31";
}
.ilmosys-geo2-love:before {
  content: "\ec32";
}
.ilmosys-geo2-number:before {
  content: "\ec33";
}
.ilmosys-geo2-star:before {
  content: "\ec34";
}
.ilmosys-geo32:before {
  content: "\ec35";
}
.ilmosys-geo33:before {
  content: "\ec36";
}
.ilmosys-geo34:before {
  content: "\ec37";
}
.ilmosys-geo3-close:before {
  content: "\ec38";
}
.ilmosys-geo3-love:before {
  content: "\ec39";
}
.ilmosys-geo3-number:before {
  content: "\ec3a";
}
.ilmosys-geo3-star:before {
  content: "\ec3b";
}
.ilmosys-geo-close:before {
  content: "\ec3c";
}
.ilmosys-geo-love:before {
  content: "\ec3d";
}
.ilmosys-geo-number:before {
  content: "\ec3e";
}
.ilmosys-geo-star:before {
  content: "\ec3f";
}
.ilmosys-gey:before {
  content: "\ec40";
}
.ilmosys-gift-box:before {
  content: "\ec41";
}
.ilmosys-giraffe:before {
  content: "\ec42";
}
.ilmosys-girl:before {
  content: "\ec43";
}
.ilmosys-glasses:before {
  content: "\ec44";
}
.ilmosys-glasses-2:before {
  content: "\ec45";
}
.ilmosys-glasses-3:before {
  content: "\ec46";
}
.ilmosys-glass-water:before {
  content: "\ec47";
}
.ilmosys-global-position:before {
  content: "\ec48";
}
.ilmosys-globe:before {
  content: "\ec49";
}
.ilmosys-globe-2:before {
  content: "\ec4a";
}
.ilmosys-gloves:before {
  content: "\ec4b";
}
.ilmosys-go-bottom:before {
  content: "\ec4c";
}
.ilmosys-goggles:before {
  content: "\ec4d";
}
.ilmosys-golf:before {
  content: "\ec4e";
}
.ilmosys-golf-2:before {
  content: "\ec4f";
}
.ilmosys-gopro:before {
  content: "\ec50";
}
.ilmosys-gorilla:before {
  content: "\ec51";
}
.ilmosys-go-top:before {
  content: "\ec52";
}
.ilmosys-grave:before {
  content: "\ec53";
}
.ilmosys-graveyard:before {
  content: "\ec54";
}
.ilmosys-greece:before {
  content: "\ec55";
}
.ilmosys-green-energy:before {
  content: "\ec56";
}
.ilmosys-green-house:before {
  content: "\ec57";
}
.ilmosys-guitar:before {
  content: "\ec58";
}
.ilmosys-gun:before {
  content: "\ec59";
}
.ilmosys-gun-2:before {
  content: "\ec5a";
}
.ilmosys-gun-3:before {
  content: "\ec5b";
}
.ilmosys-gymnastics:before {
  content: "\ec5c";
}
.ilmosys-hair:before {
  content: "\ec5d";
}
.ilmosys-hair-2:before {
  content: "\ec5e";
}
.ilmosys-hair-3:before {
  content: "\ec5f";
}
.ilmosys-hair-4:before {
  content: "\ec60";
}
.ilmosys-half-moon:before {
  content: "\ec61";
}
.ilmosys-halloween-halfmoon:before {
  content: "\ec62";
}
.ilmosys-halloween-moon:before {
  content: "\ec63";
}
.ilmosys-hamburger:before {
  content: "\ec64";
}
.ilmosys-hammer:before {
  content: "\ec65";
}
.ilmosys-hand:before {
  content: "\ec66";
}
.ilmosys-hands:before {
  content: "\ec67";
}
.ilmosys-handshake:before {
  content: "\ec68";
}
.ilmosys-hand-touch:before {
  content: "\ec69";
}
.ilmosys-hand-touch2:before {
  content: "\ec6a";
}
.ilmosys-hand-touchsmartphone:before {
  content: "\ec6b";
}
.ilmosys-hanger:before {
  content: "\ec6c";
}
.ilmosys-happy:before {
  content: "\ec6d";
}
.ilmosys-hat:before {
  content: "\ec6e";
}
.ilmosys-hat-2:before {
  content: "\ec6f";
}
.ilmosys-haunted-house:before {
  content: "\ec70";
}
.ilmosys-hd:before {
  content: "\ec71";
}
.ilmosys-hdd:before {
  content: "\ec72";
}
.ilmosys-hd-video:before {
  content: "\ec73";
}
.ilmosys-headphone:before {
  content: "\ec74";
}
.ilmosys-headphones:before {
  content: "\ec75";
}
.ilmosys-headset:before {
  content: "\ec76";
}
.ilmosys-heart:before {
  content: "\ec77";
}
.ilmosys-heart-2:before {
  content: "\ec78";
}
.ilmosys-heels:before {
  content: "\ec79";
}
.ilmosys-heels-2:before {
  content: "\ec7a";
}
.ilmosys-height-window:before {
  content: "\ec7b";
}
.ilmosys-helicopter:before {
  content: "\ec7c";
}
.ilmosys-helicopter-2:before {
  content: "\ec7d";
}
.ilmosys-helix-2:before {
  content: "\ec7e";
}
.ilmosys-hello:before {
  content: "\ec7f";
}
.ilmosys-helmet:before {
  content: "\ec80";
}
.ilmosys-helmet-2:before {
  content: "\ec81";
}
.ilmosys-helmet-3:before {
  content: "\ec82";
}
.ilmosys-hipo:before {
  content: "\ec83";
}
.ilmosys-hipster-glasses:before {
  content: "\ec84";
}
.ilmosys-hipster-glasses2:before {
  content: "\ec85";
}
.ilmosys-hipster-glasses3:before {
  content: "\ec86";
}
.ilmosys-hipster-headphones:before {
  content: "\ec87";
}
.ilmosys-hipster-men:before {
  content: "\ec88";
}
.ilmosys-hipster-men2:before {
  content: "\ec89";
}
.ilmosys-hipster-men3:before {
  content: "\ec8a";
}
.ilmosys-hipster-sunglasses:before {
  content: "\ec8b";
}
.ilmosys-hipster-sunglasses2:before {
  content: "\ec8c";
}
.ilmosys-hipster-sunglasses3:before {
  content: "\ec8d";
}
.ilmosys-hokey:before {
  content: "\ec8e";
}
.ilmosys-holly:before {
  content: "\ec8f";
}
.ilmosys-home:before {
  content: "\ec90";
}
.ilmosys-home-2:before {
  content: "\ec91";
}
.ilmosys-home-3:before {
  content: "\ec92";
}
.ilmosys-home-4:before {
  content: "\ec93";
}
.ilmosys-home-5:before {
  content: "\ec94";
}
.ilmosys-home-window:before {
  content: "\ec95";
}
.ilmosys-homosexual:before {
  content: "\ec96";
}
.ilmosys-honey:before {
  content: "\ec97";
}
.ilmosys-hong-kong:before {
  content: "\ec98";
}
.ilmosys-hoodie:before {
  content: "\ec99";
}
.ilmosys-horror:before {
  content: "\ec9a";
}
.ilmosys-horse:before {
  content: "\ec9b";
}
.ilmosys-hospital:before {
  content: "\ec9c";
}
.ilmosys-hospital-2:before {
  content: "\ec9d";
}
.ilmosys-host:before {
  content: "\ec9e";
}
.ilmosys-hot-dog:before {
  content: "\ec9f";
}
.ilmosys-hotel:before {
  content: "\eca0";
}
.ilmosys-hour:before {
  content: "\eca1";
}
.ilmosys-hub:before {
  content: "\eca2";
}
.ilmosys-humor:before {
  content: "\eca3";
}
.ilmosys-hurt:before {
  content: "\eca4";
}
.ilmosys-ice-cream:before {
  content: "\eca5";
}
.ilmosys-id-2:before {
  content: "\eca6";
}
.ilmosys-id-3:before {
  content: "\eca7";
}
.ilmosys-id-card:before {
  content: "\eca8";
}
.ilmosys-idea:before {
  content: "\eca9";
}
.ilmosys-idea-2:before {
  content: "\ecaa";
}
.ilmosys-idea-3:before {
  content: "\ecab";
}
.ilmosys-idea-4:before {
  content: "\ecac";
}
.ilmosys-idea-5:before {
  content: "\ecad";
}
.ilmosys-identification-badge:before {
  content: "\ecae";
}
.ilmosys-inbox:before {
  content: "\ecaf";
}
.ilmosys-inbox-empty:before {
  content: "\ecb0";
}
.ilmosys-inbox-forward:before {
  content: "\ecb1";
}
.ilmosys-inbox-full:before {
  content: "\ecb2";
}
.ilmosys-inbox-into:before {
  content: "\ecb3";
}
.ilmosys-inbox-out:before {
  content: "\ecb4";
}
.ilmosys-inbox-reply:before {
  content: "\ecb5";
}
.ilmosys-increase-inedit:before {
  content: "\ecb6";
}
.ilmosys-indent-firstline:before {
  content: "\ecb7";
}
.ilmosys-indent-leftmargin:before {
  content: "\ecb8";
}
.ilmosys-indent-rightmargin:before {
  content: "\ecb9";
}
.ilmosys-india:before {
  content: "\ecba";
}
.ilmosys-information:before {
  content: "\ecbb";
}
.ilmosys-info-window:before {
  content: "\ecbc";
}
.ilmosys-inifity:before {
  content: "\ecbd";
}
.ilmosys-internet:before {
  content: "\ecbe";
}
.ilmosys-internet-2:before {
  content: "\ecbf";
}
.ilmosys-internet-smiley:before {
  content: "\ecc0";
}
.ilmosys-israel:before {
  content: "\ecc1";
}
.ilmosys-italic-text:before {
  content: "\ecc2";
}
.ilmosys-jacket:before {
  content: "\ecc3";
}
.ilmosys-jacket-2:before {
  content: "\ecc4";
}
.ilmosys-jamaica:before {
  content: "\ecc5";
}
.ilmosys-japan:before {
  content: "\ecc6";
}
.ilmosys-japanese-gate:before {
  content: "\ecc7";
}
.ilmosys-jeans:before {
  content: "\ecc8";
}
.ilmosys-jeep:before {
  content: "\ecc9";
}
.ilmosys-jeep-2:before {
  content: "\ecca";
}
.ilmosys-jet:before {
  content: "\eccb";
}
.ilmosys-joystick:before {
  content: "\eccc";
}
.ilmosys-juice:before {
  content: "\eccd";
}
.ilmosys-jump-rope:before {
  content: "\ecce";
}
.ilmosys-kangoroo:before {
  content: "\eccf";
}
.ilmosys-kenya:before {
  content: "\ecd0";
}
.ilmosys-key:before {
  content: "\ecd1";
}
.ilmosys-key-2:before {
  content: "\ecd2";
}
.ilmosys-key-3:before {
  content: "\ecd3";
}
.ilmosys-keyboard:before {
  content: "\ecd4";
}
.ilmosys-keyboard3:before {
  content: "\ecd5";
}
.ilmosys-key-lock:before {
  content: "\ecd6";
}
.ilmosys-keypad:before {
  content: "\ecd7";
}
.ilmosys-king:before {
  content: "\ecd8";
}
.ilmosys-king-2:before {
  content: "\ecd9";
}
.ilmosys-kiss:before {
  content: "\ecda";
}
.ilmosys-knee:before {
  content: "\ecdb";
}
.ilmosys-knife:before {
  content: "\ecdc";
}
.ilmosys-knife-2:before {
  content: "\ecdd";
}
.ilmosys-knight:before {
  content: "\ecde";
}
.ilmosys-koala:before {
  content: "\ecdf";
}
.ilmosys-korea:before {
  content: "\ece0";
}
.ilmosys-lamp:before {
  content: "\ece1";
}
.ilmosys-landscape:before {
  content: "\ece2";
}
.ilmosys-landscape-2:before {
  content: "\ece3";
}
.ilmosys-lantern:before {
  content: "\ece4";
}
.ilmosys-laptop:before {
  content: "\ece5";
}
.ilmosys-laptop-2:before {
  content: "\ece6";
}
.ilmosys-laptop-3:before {
  content: "\ece7";
}
.ilmosys-laptop-phone:before {
  content: "\ece8";
}
.ilmosys-laptop-secure:before {
  content: "\ece9";
}
.ilmosys-laptop-tablet:before {
  content: "\ecea";
}
.ilmosys-laser:before {
  content: "\eceb";
}
.ilmosys-last:before {
  content: "\ecec";
}
.ilmosys-laughing:before {
  content: "\eced";
}
.ilmosys-layer-backward:before {
  content: "\ecee";
}
.ilmosys-layer-forward:before {
  content: "\ecef";
}
.ilmosys-leafs:before {
  content: "\ecf0";
}
.ilmosys-leafs-2:before {
  content: "\ecf1";
}
.ilmosys-leaning-tower:before {
  content: "\ecf2";
}
.ilmosys-left:before {
  content: "\ecf3";
}
.ilmosys-left-2:before {
  content: "\ecf4";
}
.ilmosys-left-3:before {
  content: "\ecf5";
}
.ilmosys-left-4:before {
  content: "\ecf6";
}
.ilmosys-left--right:before {
  content: "\ecf7";
}
.ilmosys-left--right3:before {
  content: "\ecf8";
}
.ilmosys-left-toright:before {
  content: "\ecf9";
}
.ilmosys-leg:before {
  content: "\ecfa";
}
.ilmosys-leg-2:before {
  content: "\ecfb";
}
.ilmosys-lego:before {
  content: "\ecfc";
}
.ilmosys-lemon:before {
  content: "\ecfd";
}
.ilmosys-len:before {
  content: "\ecfe";
}
.ilmosys-len-2:before {
  content: "\ecff";
}
.ilmosys-len-3:before {
  content: "\ed00";
}
.ilmosys-leo:before {
  content: "\ed01";
}
.ilmosys-leo-2:before {
  content: "\ed02";
}
.ilmosys-leopard:before {
  content: "\ed03";
}
.ilmosys-lesbian:before {
  content: "\ed04";
}
.ilmosys-lesbians:before {
  content: "\ed05";
}
.ilmosys-letter-close:before {
  content: "\ed06";
}
.ilmosys-letter-open:before {
  content: "\ed07";
}
.ilmosys-letter-sent:before {
  content: "\ed08";
}
.ilmosys-libra:before {
  content: "\ed09";
}
.ilmosys-libra-2:before {
  content: "\ed0a";
}
.ilmosys-library:before {
  content: "\ed0b";
}
.ilmosys-library-2:before {
  content: "\ed0c";
}
.ilmosys-life-jacket:before {
  content: "\ed0d";
}
.ilmosys-life-safer:before {
  content: "\ed0e";
}
.ilmosys-light-bulb:before {
  content: "\ed0f";
}
.ilmosys-light-bulb2:before {
  content: "\ed10";
}
.ilmosys-light-bulbleaf:before {
  content: "\ed11";
}
.ilmosys-lighthouse:before {
  content: "\ed12";
}
.ilmosys-line-chart:before {
  content: "\ed13";
}
.ilmosys-line-chart2:before {
  content: "\ed14";
}
.ilmosys-line-chart3:before {
  content: "\ed15";
}
.ilmosys-line-chart4:before {
  content: "\ed16";
}
.ilmosys-line-spacing:before {
  content: "\ed17";
}
.ilmosys-line-spacingtext:before {
  content: "\ed18";
}
.ilmosys-link:before {
  content: "\ed19";
}
.ilmosys-link-2:before {
  content: "\ed1a";
}
.ilmosys-lion:before {
  content: "\ed1b";
}
.ilmosys-loading:before {
  content: "\ed1c";
}
.ilmosys-loading-2:before {
  content: "\ed1d";
}
.ilmosys-loading-3:before {
  content: "\ed1e";
}
.ilmosys-loading-window:before {
  content: "\ed1f";
}
.ilmosys-location:before {
  content: "\ed20";
}
.ilmosys-location-2:before {
  content: "\ed21";
}
.ilmosys-lock:before {
  content: "\ed22";
}
.ilmosys-lock-2:before {
  content: "\ed23";
}
.ilmosys-lock-3:before {
  content: "\ed24";
}
.ilmosys-lock-user:before {
  content: "\ed25";
}
.ilmosys-lock-window:before {
  content: "\ed26";
}
.ilmosys-lollipop:before {
  content: "\ed27";
}
.ilmosys-lollipop-2:before {
  content: "\ed28";
}
.ilmosys-lollipop-3:before {
  content: "\ed29";
}
.ilmosys-loop:before {
  content: "\ed2a";
}
.ilmosys-loud:before {
  content: "\ed2b";
}
.ilmosys-loudspeaker:before {
  content: "\ed2c";
}
.ilmosys-love:before {
  content: "\ed2d";
}
.ilmosys-love-2:before {
  content: "\ed2e";
}
.ilmosys-love-user:before {
  content: "\ed2f";
}
.ilmosys-love-window:before {
  content: "\ed30";
}
.ilmosys-lowercase-text:before {
  content: "\ed31";
}
.ilmosys-luggafe-front:before {
  content: "\ed32";
}
.ilmosys-luggage-2:before {
  content: "\ed33";
}
.ilmosys-macro:before {
  content: "\ed34";
}
.ilmosys-magic-wand:before {
  content: "\ed35";
}
.ilmosys-magnet:before {
  content: "\ed36";
}
.ilmosys-magnifi-glass:before {
  content: "\ed37";
}
.ilmosys-magnifi-glass2:before {
  content: "\ed38";
}
.ilmosys-magnifi-glass22:before {
  content: "\ed39";
}
.ilmosys-mail:before {
  content: "\ed3a";
}
.ilmosys-mail-2:before {
  content: "\ed3b";
}
.ilmosys-mail-3:before {
  content: "\ed3c";
}
.ilmosys-mail-add:before {
  content: "\ed3d";
}
.ilmosys-mail-attachement:before {
  content: "\ed3e";
}
.ilmosys-mail-block:before {
  content: "\ed3f";
}
.ilmosys-mailbox-empty:before {
  content: "\ed40";
}
.ilmosys-mailbox-full:before {
  content: "\ed41";
}
.ilmosys-mail-delete:before {
  content: "\ed42";
}
.ilmosys-mail-favorite:before {
  content: "\ed43";
}
.ilmosys-mail-forward:before {
  content: "\ed44";
}
.ilmosys-mail-gallery:before {
  content: "\ed45";
}
.ilmosys-mail-inbox:before {
  content: "\ed46";
}
.ilmosys-mail-link:before {
  content: "\ed47";
}
.ilmosys-mail-lock:before {
  content: "\ed48";
}
.ilmosys-mail-love:before {
  content: "\ed49";
}
.ilmosys-mail-money:before {
  content: "\ed4a";
}
.ilmosys-mail-open:before {
  content: "\ed4b";
}
.ilmosys-mail-outbox:before {
  content: "\ed4c";
}
.ilmosys-mail-password:before {
  content: "\ed4d";
}
.ilmosys-mail-photo:before {
  content: "\ed4e";
}
.ilmosys-mail-read:before {
  content: "\ed4f";
}
.ilmosys-mail-removex:before {
  content: "\ed50";
}
.ilmosys-mail-reply:before {
  content: "\ed51";
}
.ilmosys-mail-replyall:before {
  content: "\ed52";
}
.ilmosys-mail-search:before {
  content: "\ed53";
}
.ilmosys-mail-send:before {
  content: "\ed54";
}
.ilmosys-mail-settings:before {
  content: "\ed55";
}
.ilmosys-mail-unread:before {
  content: "\ed56";
}
.ilmosys-mail-video:before {
  content: "\ed57";
}
.ilmosys-mail-withatsign:before {
  content: "\ed58";
}
.ilmosys-mail-withcursors:before {
  content: "\ed59";
}
.ilmosys-male:before {
  content: "\ed5a";
}
.ilmosys-male-2:before {
  content: "\ed5b";
}
.ilmosys-malefemale:before {
  content: "\ed5c";
}
.ilmosys-male-sign:before {
  content: "\ed5d";
}
.ilmosys-management:before {
  content: "\ed5e";
}
.ilmosys-man-sign:before {
  content: "\ed5f";
}
.ilmosys-mans-underwear:before {
  content: "\ed60";
}
.ilmosys-mans-underwear2:before {
  content: "\ed61";
}
.ilmosys-map:before {
  content: "\ed62";
}
.ilmosys-map2:before {
  content: "\ed63";
}
.ilmosys-map-marker:before {
  content: "\ed64";
}
.ilmosys-map-marker2:before {
  content: "\ed65";
}
.ilmosys-map-marker3:before {
  content: "\ed66";
}
.ilmosys-marker:before {
  content: "\ed67";
}
.ilmosys-marker-2:before {
  content: "\ed68";
}
.ilmosys-marker-3:before {
  content: "\ed69";
}
.ilmosys-martini-glass:before {
  content: "\ed6a";
}
.ilmosys-mask:before {
  content: "\ed6b";
}
.ilmosys-master-card:before {
  content: "\ed6c";
}
.ilmosys-maximize:before {
  content: "\ed6d";
}
.ilmosys-maximize-window:before {
  content: "\ed6e";
}
.ilmosys-medal:before {
  content: "\ed6f";
}
.ilmosys-medal-2:before {
  content: "\ed70";
}
.ilmosys-medal-3:before {
  content: "\ed71";
}
.ilmosys-medical-sign:before {
  content: "\ed72";
}
.ilmosys-medicine:before {
  content: "\ed73";
}
.ilmosys-medicine-2:before {
  content: "\ed74";
}
.ilmosys-medicine-3:before {
  content: "\ed75";
}
.ilmosys-megaphone:before {
  content: "\ed76";
}
.ilmosys-memory-card:before {
  content: "\ed77";
}
.ilmosys-memory-card2:before {
  content: "\ed78";
}
.ilmosys-memory-card3:before {
  content: "\ed79";
}
.ilmosys-men:before {
  content: "\ed7a";
}
.ilmosys-menorah:before {
  content: "\ed7b";
}
.ilmosys-mens:before {
  content: "\ed7c";
}
.ilmosys-mexico:before {
  content: "\ed7d";
}
.ilmosys-mic:before {
  content: "\ed7e";
}
.ilmosys-microphone:before {
  content: "\ed7f";
}
.ilmosys-microphone-2:before {
  content: "\ed80";
}
.ilmosys-microphone-3:before {
  content: "\ed81";
}
.ilmosys-microphone-4:before {
  content: "\ed82";
}
.ilmosys-microphone-5:before {
  content: "\ed83";
}
.ilmosys-microphone-6:before {
  content: "\ed84";
}
.ilmosys-microphone-7:before {
  content: "\ed85";
}
.ilmosys-microscope:before {
  content: "\ed86";
}
.ilmosys-milk-bottle:before {
  content: "\ed87";
}
.ilmosys-mine:before {
  content: "\ed88";
}
.ilmosys-minimize:before {
  content: "\ed89";
}
.ilmosys-minimize-maximize-close-window:before {
  content: "\ed8a";
}
.ilmosys-minimize-window:before {
  content: "\ed8b";
}
.ilmosys-mirror:before {
  content: "\ed8c";
}
.ilmosys-mixer:before {
  content: "\ed8d";
}
.ilmosys-money:before {
  content: "\ed8e";
}
.ilmosys-money-2:before {
  content: "\ed8f";
}
.ilmosys-money-bag:before {
  content: "\ed90";
}
.ilmosys-money-smiley:before {
  content: "\ed91";
}
.ilmosys-monitor:before {
  content: "\ed92";
}
.ilmosys-monitor-2:before {
  content: "\ed93";
}
.ilmosys-monitor-3:before {
  content: "\ed94";
}
.ilmosys-monitor-4:before {
  content: "\ed95";
}
.ilmosys-monitor-5:before {
  content: "\ed96";
}
.ilmosys-monitor-analytics:before {
  content: "\ed97";
}
.ilmosys-monitoring:before {
  content: "\ed98";
}
.ilmosys-monitor-laptop:before {
  content: "\ed99";
}
.ilmosys-monitor-phone:before {
  content: "\ed9a";
}
.ilmosys-monitor-tablet:before {
  content: "\ed9b";
}
.ilmosys-monitor-vertical:before {
  content: "\ed9c";
}
.ilmosys-monkey:before {
  content: "\ed9d";
}
.ilmosys-monster:before {
  content: "\ed9e";
}
.ilmosys-morocco:before {
  content: "\ed9f";
}
.ilmosys-motorcycle:before {
  content: "\eda0";
}
.ilmosys-mouse:before {
  content: "\eda1";
}
.ilmosys-mouse-2:before {
  content: "\eda2";
}
.ilmosys-mouse-3:before {
  content: "\eda3";
}
.ilmosys-mouse-4:before {
  content: "\eda4";
}
.ilmosys-mouse-pointer:before {
  content: "\eda5";
}
.ilmosys-moustache-smiley:before {
  content: "\eda6";
}
.ilmosys-movie:before {
  content: "\eda7";
}
.ilmosys-movie-ticket:before {
  content: "\eda8";
}
.ilmosys-mp3-file:before {
  content: "\eda9";
}
.ilmosys-museum:before {
  content: "\edaa";
}
.ilmosys-mushroom:before {
  content: "\edab";
}
.ilmosys-music-note:before {
  content: "\edac";
}
.ilmosys-music-note2:before {
  content: "\edad";
}
.ilmosys-music-note3:before {
  content: "\edae";
}
.ilmosys-music-note4:before {
  content: "\edaf";
}
.ilmosys-music-player:before {
  content: "\edb0";
}
.ilmosys-mustache:before {
  content: "\edb1";
}
.ilmosys-mustache-2:before {
  content: "\edb2";
}
.ilmosys-mustache-3:before {
  content: "\edb3";
}
.ilmosys-mustache-4:before {
  content: "\edb4";
}
.ilmosys-mustache-5:before {
  content: "\edb5";
}
.ilmosys-mustache-6:before {
  content: "\edb6";
}
.ilmosys-mustache-7:before {
  content: "\edb7";
}
.ilmosys-mustache-8:before {
  content: "\edb8";
}
.ilmosys-mute:before {
  content: "\edb9";
}
.ilmosys-navigate-end:before {
  content: "\edba";
}
.ilmosys-navigation-leftwindow:before {
  content: "\edbb";
}
.ilmosys-navigation-rightwindow:before {
  content: "\edbc";
}
.ilmosys-navigat-start:before {
  content: "\edbd";
}
.ilmosys-nepal:before {
  content: "\edbe";
}
.ilmosys-network:before {
  content: "\edbf";
}
.ilmosys-network-window:before {
  content: "\edc0";
}
.ilmosys-neutron:before {
  content: "\edc1";
}
.ilmosys-new-mail:before {
  content: "\edc2";
}
.ilmosys-newspaper:before {
  content: "\edc3";
}
.ilmosys-newspaper-2:before {
  content: "\edc4";
}
.ilmosys-new-tab:before {
  content: "\edc5";
}
.ilmosys-next:before {
  content: "\edc6";
}
.ilmosys-next-3:before {
  content: "\edc7";
}
.ilmosys-next-music:before {
  content: "\edc8";
}
.ilmosys-no-battery:before {
  content: "\edc9";
}
.ilmosys-no-drop:before {
  content: "\edca";
}
.ilmosys-no-flash:before {
  content: "\edcb";
}
.ilmosys-noose:before {
  content: "\edcc";
}
.ilmosys-normal-text:before {
  content: "\edcd";
}
.ilmosys-no-smoking:before {
  content: "\edce";
}
.ilmosys-note:before {
  content: "\edcf";
}
.ilmosys-notepad:before {
  content: "\edd0";
}
.ilmosys-notepad-2:before {
  content: "\edd1";
}
.ilmosys-nuclear:before {
  content: "\edd2";
}
.ilmosys-numbering-list:before {
  content: "\edd3";
}
.ilmosys-nurse:before {
  content: "\edd4";
}
.ilmosys-office:before {
  content: "\edd5";
}
.ilmosys-office-lamp:before {
  content: "\edd6";
}
.ilmosys-oil:before {
  content: "\edd7";
}
.ilmosys-old-camera:before {
  content: "\edd8";
}
.ilmosys-old-cassette:before {
  content: "\edd9";
}
.ilmosys-old-clock:before {
  content: "\edda";
}
.ilmosys-old-radio:before {
  content: "\eddb";
}
.ilmosys-old-sticky:before {
  content: "\eddc";
}
.ilmosys-old-sticky2:before {
  content: "\eddd";
}
.ilmosys-old-telephone:before {
  content: "\edde";
}
.ilmosys-old-tv:before {
  content: "\eddf";
}
.ilmosys-on-air:before {
  content: "\ede0";
}
.ilmosys-one-finger:before {
  content: "\ede1";
}
.ilmosys-one-fingertouch:before {
  content: "\ede2";
}
.ilmosys-one-window:before {
  content: "\ede3";
}
.ilmosys-on-off:before {
  content: "\ede4";
}
.ilmosys-on-off-2:before {
  content: "\ede5";
}
.ilmosys-on-off-3:before {
  content: "\ede6";
}
.ilmosys-open-banana:before {
  content: "\ede7";
}
.ilmosys-open-book:before {
  content: "\ede8";
}
.ilmosys-opera-house:before {
  content: "\ede9";
}
.ilmosys-optimization:before {
  content: "\edea";
}
.ilmosys-orientation:before {
  content: "\edeb";
}
.ilmosys-orientation-2:before {
  content: "\edec";
}
.ilmosys-orientation-3:before {
  content: "\eded";
}
.ilmosys-ornament:before {
  content: "\edee";
}
.ilmosys-over-time:before {
  content: "\edef";
}
.ilmosys-over-time2:before {
  content: "\edf0";
}
.ilmosys-owl:before {
  content: "\edf1";
}
.ilmosys-pac-man:before {
  content: "\edf2";
}
.ilmosys-paintbrush:before {
  content: "\edf3";
}
.ilmosys-paint-brush:before {
  content: "\edf4";
}
.ilmosys-paint-bucket:before {
  content: "\edf5";
}
.ilmosys-palette:before {
  content: "\edf6";
}
.ilmosys-palm-tree:before {
  content: "\edf7";
}
.ilmosys-panda:before {
  content: "\edf8";
}
.ilmosys-panorama:before {
  content: "\edf9";
}
.ilmosys-pantheon:before {
  content: "\edfa";
}
.ilmosys-pantone:before {
  content: "\edfb";
}
.ilmosys-pants:before {
  content: "\edfc";
}
.ilmosys-paper:before {
  content: "\edfd";
}
.ilmosys-paper-plane:before {
  content: "\edfe";
}
.ilmosys-parasailing:before {
  content: "\edff";
}
.ilmosys-parrot:before {
  content: "\ee00";
}
.ilmosys-password:before {
  content: "\ee01";
}
.ilmosys-password-2shopping:before {
  content: "\ee02";
}
.ilmosys-password-field:before {
  content: "\ee03";
}
.ilmosys-password-shopping:before {
  content: "\ee04";
}
.ilmosys-pause:before {
  content: "\ee05";
}
.ilmosys-pause-2:before {
  content: "\ee06";
}
.ilmosys-paw:before {
  content: "\ee07";
}
.ilmosys-pawn:before {
  content: "\ee08";
}
.ilmosys-pen:before {
  content: "\ee09";
}
.ilmosys-pen-2:before {
  content: "\ee0a";
}
.ilmosys-pen-3:before {
  content: "\ee0b";
}
.ilmosys-pen-4:before {
  content: "\ee0c";
}
.ilmosys-pen-5:before {
  content: "\ee0d";
}
.ilmosys-pen-6:before {
  content: "\ee0e";
}
.ilmosys-pencil:before {
  content: "\ee0f";
}
.ilmosys-pencil-ruler:before {
  content: "\ee10";
}
.ilmosys-penguin:before {
  content: "\ee11";
}
.ilmosys-pentagon:before {
  content: "\ee12";
}
.ilmosys-people-oncloud:before {
  content: "\ee13";
}
.ilmosys-pepper:before {
  content: "\ee14";
}
.ilmosys-pepper-withfire:before {
  content: "\ee15";
}
.ilmosys-petrol:before {
  content: "\ee16";
}
.ilmosys-petronas-tower:before {
  content: "\ee17";
}
.ilmosys-philipines:before {
  content: "\ee18";
}
.ilmosys-phone:before {
  content: "\ee19";
}
.ilmosys-phone-2:before {
  content: "\ee1a";
}
.ilmosys-phone-3:before {
  content: "\ee1b";
}
.ilmosys-phone-3g:before {
  content: "\ee1c";
}
.ilmosys-phone-4g:before {
  content: "\ee1d";
}
.ilmosys-phone-simcard:before {
  content: "\ee1e";
}
.ilmosys-phone-sms:before {
  content: "\ee1f";
}
.ilmosys-phone-wifi:before {
  content: "\ee20";
}
.ilmosys-photo:before {
  content: "\ee21";
}
.ilmosys-photo-2:before {
  content: "\ee22";
}
.ilmosys-photo-3:before {
  content: "\ee23";
}
.ilmosys-photo-album:before {
  content: "\ee24";
}
.ilmosys-photo-album2:before {
  content: "\ee25";
}
.ilmosys-photo-album3:before {
  content: "\ee26";
}
.ilmosys-photos:before {
  content: "\ee27";
}
.ilmosys-physics:before {
  content: "\ee28";
}
.ilmosys-pi:before {
  content: "\ee29";
}
.ilmosys-piano:before {
  content: "\ee2a";
}
.ilmosys-pie-chart:before {
  content: "\ee2b";
}
.ilmosys-pie-chart2:before {
  content: "\ee2c";
}
.ilmosys-pie-chart3:before {
  content: "\ee2d";
}
.ilmosys-pilates:before {
  content: "\ee2e";
}
.ilmosys-pilates-2:before {
  content: "\ee2f";
}
.ilmosys-pilates-3:before {
  content: "\ee30";
}
.ilmosys-pilot:before {
  content: "\ee31";
}
.ilmosys-pinch:before {
  content: "\ee32";
}
.ilmosys-ping-pong:before {
  content: "\ee33";
}
.ilmosys-pipe:before {
  content: "\ee34";
}
.ilmosys-pipette:before {
  content: "\ee35";
}
.ilmosys-piramids:before {
  content: "\ee36";
}
.ilmosys-pisces:before {
  content: "\ee37";
}
.ilmosys-pisces-2:before {
  content: "\ee38";
}
.ilmosys-pizza:before {
  content: "\ee39";
}
.ilmosys-pizza-slice:before {
  content: "\ee3a";
}
.ilmosys-plane:before {
  content: "\ee3b";
}
.ilmosys-plane-2:before {
  content: "\ee3c";
}
.ilmosys-plant:before {
  content: "\ee3d";
}
.ilmosys-plasmid:before {
  content: "\ee3e";
}
.ilmosys-plaster:before {
  content: "\ee3f";
}
.ilmosys-plastic-cupphone:before {
  content: "\ee40";
}
.ilmosys-plastic-cupphone2:before {
  content: "\ee41";
}
.ilmosys-plate:before {
  content: "\ee42";
}
.ilmosys-plates:before {
  content: "\ee43";
}
.ilmosys-play-music:before {
  content: "\ee44";
}
.ilmosys-plug-in:before {
  content: "\ee45";
}
.ilmosys-plug-in2:before {
  content: "\ee46";
}
.ilmosys-pointer:before {
  content: "\ee47";
}
.ilmosys-poland:before {
  content: "\ee48";
}
.ilmosys-police:before {
  content: "\ee49";
}
.ilmosys-police-man:before {
  content: "\ee4a";
}
.ilmosys-police-station:before {
  content: "\ee4b";
}
.ilmosys-police-woman:before {
  content: "\ee4c";
}
.ilmosys-polo-shirt:before {
  content: "\ee4d";
}
.ilmosys-portrait:before {
  content: "\ee4e";
}
.ilmosys-portugal:before {
  content: "\ee4f";
}
.ilmosys-post-mail:before {
  content: "\ee50";
}
.ilmosys-post-mail2:before {
  content: "\ee51";
}
.ilmosys-post-office:before {
  content: "\ee52";
}
.ilmosys-post-sign:before {
  content: "\ee53";
}
.ilmosys-post-sign2ways:before {
  content: "\ee54";
}
.ilmosys-pound:before {
  content: "\ee55";
}
.ilmosys-pound-sign:before {
  content: "\ee56";
}
.ilmosys-pound-sign2:before {
  content: "\ee57";
}
.ilmosys-power:before {
  content: "\ee58";
}
.ilmosys-power-2:before {
  content: "\ee59";
}
.ilmosys-power-3:before {
  content: "\ee5a";
}
.ilmosys-power-cable:before {
  content: "\ee5b";
}
.ilmosys-power-station:before {
  content: "\ee5c";
}
.ilmosys-prater:before {
  content: "\ee5d";
}
.ilmosys-present:before {
  content: "\ee5e";
}
.ilmosys-presents:before {
  content: "\ee5f";
}
.ilmosys-press:before {
  content: "\ee60";
}
.ilmosys-preview:before {
  content: "\ee61";
}
.ilmosys-previous:before {
  content: "\ee62";
}
.ilmosys-pricing:before {
  content: "\ee63";
}
.ilmosys-printer:before {
  content: "\ee64";
}
.ilmosys-professor:before {
  content: "\ee65";
}
.ilmosys-profile:before {
  content: "\ee66";
}
.ilmosys-project:before {
  content: "\ee67";
}
.ilmosys-projector:before {
  content: "\ee68";
}
.ilmosys-projector-2:before {
  content: "\ee69";
}
.ilmosys-pulse:before {
  content: "\ee6a";
}
.ilmosys-pumpkin:before {
  content: "\ee6b";
}
.ilmosys-punk:before {
  content: "\ee6c";
}
.ilmosys-punker:before {
  content: "\ee6d";
}
.ilmosys-puzzle:before {
  content: "\ee6e";
}
.ilmosys-qr-code:before {
  content: "\ee6f";
}
.ilmosys-queen:before {
  content: "\ee70";
}
.ilmosys-queen-2:before {
  content: "\ee71";
}
.ilmosys-quill:before {
  content: "\ee72";
}
.ilmosys-quill-2:before {
  content: "\ee73";
}
.ilmosys-quill-3:before {
  content: "\ee74";
}
.ilmosys-quotes:before {
  content: "\ee75";
}
.ilmosys-quotes-2:before {
  content: "\ee76";
}
.ilmosys-radio:before {
  content: "\ee77";
}
.ilmosys-radioactive:before {
  content: "\ee78";
}
.ilmosys-rafting:before {
  content: "\ee79";
}
.ilmosys-rainbow:before {
  content: "\ee7a";
}
.ilmosys-rainbow-2:before {
  content: "\ee7b";
}
.ilmosys-rain-drop:before {
  content: "\ee7c";
}
.ilmosys-ram:before {
  content: "\ee7d";
}
.ilmosys-razzor-blade:before {
  content: "\ee7e";
}
.ilmosys-receipt:before {
  content: "\ee7f";
}
.ilmosys-receipt-2:before {
  content: "\ee80";
}
.ilmosys-receipt-3:before {
  content: "\ee81";
}
.ilmosys-receipt-4:before {
  content: "\ee82";
}
.ilmosys-record:before {
  content: "\ee83";
}
.ilmosys-record-3:before {
  content: "\ee84";
}
.ilmosys-record-music:before {
  content: "\ee85";
}
.ilmosys-recycling:before {
  content: "\ee86";
}
.ilmosys-recycling-2:before {
  content: "\ee87";
}
.ilmosys-redirect:before {
  content: "\ee88";
}
.ilmosys-redo:before {
  content: "\ee89";
}
.ilmosys-reel:before {
  content: "\ee8a";
}
.ilmosys-refinery:before {
  content: "\ee8b";
}
.ilmosys-refresh:before {
  content: "\ee8c";
}
.ilmosys-refresh-window:before {
  content: "\ee8d";
}
.ilmosys-reload:before {
  content: "\ee8e";
}
.ilmosys-reload-2:before {
  content: "\ee8f";
}
.ilmosys-reload-3:before {
  content: "\ee90";
}
.ilmosys-remote-controll:before {
  content: "\ee91";
}
.ilmosys-remote-controll2:before {
  content: "\ee92";
}
.ilmosys-remove:before {
  content: "\ee93";
}
.ilmosys-remove-bag:before {
  content: "\ee94";
}
.ilmosys-remove-basket:before {
  content: "\ee95";
}
.ilmosys-remove-cart:before {
  content: "\ee96";
}
.ilmosys-remove-file:before {
  content: "\ee97";
}
.ilmosys-remove-user:before {
  content: "\ee98";
}
.ilmosys-remove-window:before {
  content: "\ee99";
}
.ilmosys-rename:before {
  content: "\ee9a";
}
.ilmosys-repair:before {
  content: "\ee9b";
}
.ilmosys-repeat:before {
  content: "\ee9c";
}
.ilmosys-repeat-2:before {
  content: "\ee9d";
}
.ilmosys-repeat-3:before {
  content: "\ee9e";
}
.ilmosys-repeat-4:before {
  content: "\ee9f";
}
.ilmosys-repeat-5:before {
  content: "\eea0";
}
.ilmosys-repeat-6:before {
  content: "\eea1";
}
.ilmosys-repeat-7:before {
  content: "\eea2";
}
.ilmosys-reset:before {
  content: "\eea3";
}
.ilmosys-resize:before {
  content: "\eea4";
}
.ilmosys-restore-window:before {
  content: "\eea5";
}
.ilmosys-retouching:before {
  content: "\eea6";
}
.ilmosys-retro:before {
  content: "\eea7";
}
.ilmosys-retro-camera:before {
  content: "\eea8";
}
.ilmosys-retweet:before {
  content: "\eea9";
}
.ilmosys-rewind:before {
  content: "\eeaa";
}
.ilmosys-rgb:before {
  content: "\eeab";
}
.ilmosys-ribbon:before {
  content: "\eeac";
}
.ilmosys-ribbon-2:before {
  content: "\eead";
}
.ilmosys-ribbon-3:before {
  content: "\eeae";
}
.ilmosys-right:before {
  content: "\eeaf";
}
.ilmosys-right-2:before {
  content: "\eeb0";
}
.ilmosys-right-3:before {
  content: "\eeb1";
}
.ilmosys-right-4:before {
  content: "\eeb2";
}
.ilmosys-right-toleft:before {
  content: "\eeb3";
}
.ilmosys-road:before {
  content: "\eeb4";
}
.ilmosys-road-2:before {
  content: "\eeb5";
}
.ilmosys-road-3:before {
  content: "\eeb6";
}
.ilmosys-robot:before {
  content: "\eeb7";
}
.ilmosys-robot-2:before {
  content: "\eeb8";
}
.ilmosys-rock-androll:before {
  content: "\eeb9";
}
.ilmosys-rocket:before {
  content: "\eeba";
}
.ilmosys-roller:before {
  content: "\eebb";
}
.ilmosys-roof:before {
  content: "\eebc";
}
.ilmosys-rook:before {
  content: "\eebd";
}
.ilmosys-rotate-gesture:before {
  content: "\eebe";
}
.ilmosys-rotate-gesture2:before {
  content: "\eebf";
}
.ilmosys-rotate-gesture3:before {
  content: "\eec0";
}
.ilmosys-rotation:before {
  content: "\eec1";
}
.ilmosys-rotation-390:before {
  content: "\eec2";
}
.ilmosys-router:before {
  content: "\eec3";
}
.ilmosys-router-2:before {
  content: "\eec4";
}
.ilmosys-ruler:before {
  content: "\eec5";
}
.ilmosys-ruler-2:before {
  content: "\eec6";
}
.ilmosys-running:before {
  content: "\eec7";
}
.ilmosys-running-shoes:before {
  content: "\eec8";
}
.ilmosys-safe-box:before {
  content: "\eec9";
}
.ilmosys-safe-box2:before {
  content: "\eeca";
}
.ilmosys-safety-pinclose:before {
  content: "\eecb";
}
.ilmosys-safety-pinopen:before {
  content: "\eecc";
}
.ilmosys-sagittarus:before {
  content: "\eecd";
}
.ilmosys-sagittarus-2:before {
  content: "\eece";
}
.ilmosys-sailing-ship:before {
  content: "\eecf";
}
.ilmosys-sand-watch:before {
  content: "\eed0";
}
.ilmosys-sand-watch2:before {
  content: "\eed1";
}
.ilmosys-santa-claus:before {
  content: "\eed2";
}
.ilmosys-santa-claus2:before {
  content: "\eed3";
}
.ilmosys-santa-onsled:before {
  content: "\eed4";
}
.ilmosys-satelite:before {
  content: "\eed5";
}
.ilmosys-satelite-2:before {
  content: "\eed6";
}
.ilmosys-save:before {
  content: "\eed7";
}
.ilmosys-save-window:before {
  content: "\eed8";
}
.ilmosys-saw:before {
  content: "\eed9";
}
.ilmosys-saxophone:before {
  content: "\eeda";
}
.ilmosys-scale:before {
  content: "\eedb";
}
.ilmosys-scarf:before {
  content: "\eedc";
}
.ilmosys-scissor:before {
  content: "\eedd";
}
.ilmosys-scooter:before {
  content: "\eede";
}
.ilmosys-scooter-front:before {
  content: "\eedf";
}
.ilmosys-scorpio:before {
  content: "\eee0";
}
.ilmosys-scorpio-2:before {
  content: "\eee1";
}
.ilmosys-scotland:before {
  content: "\eee2";
}
.ilmosys-screwdriver:before {
  content: "\eee3";
}
.ilmosys-scroll:before {
  content: "\eee4";
}
.ilmosys-scroller:before {
  content: "\eee5";
}
.ilmosys-scroller-2:before {
  content: "\eee6";
}
.ilmosys-scroll-fast:before {
  content: "\eee7";
}
.ilmosys-sea-dog:before {
  content: "\eee8";
}
.ilmosys-search-oncloud:before {
  content: "\eee9";
}
.ilmosys-search-people:before {
  content: "\eeea";
}
.ilmosys-secound:before {
  content: "\eeeb";
}
.ilmosys-secound2:before {
  content: "\eeec";
}
.ilmosys-security-block:before {
  content: "\eeed";
}
.ilmosys-security-bug:before {
  content: "\eeee";
}
.ilmosys-security-camera:before {
  content: "\eeef";
}
.ilmosys-security-check:before {
  content: "\eef0";
}
.ilmosys-security-settings:before {
  content: "\eef1";
}
.ilmosys-security-smiley:before {
  content: "\eef2";
}
.ilmosys-securiy-remove:before {
  content: "\eef3";
}
.ilmosys-seed:before {
  content: "\eef4";
}
.ilmosys-selfie:before {
  content: "\eef5";
}
.ilmosys-serbia:before {
  content: "\eef6";
}
.ilmosys-server:before {
  content: "\eef7";
}
.ilmosys-server-2:before {
  content: "\eef8";
}
.ilmosys-servers:before {
  content: "\eef9";
}
.ilmosys-settings-window:before {
  content: "\eefa";
}
.ilmosys-sewing-machine:before {
  content: "\eefb";
}
.ilmosys-sexual:before {
  content: "\eefc";
}
.ilmosys-share:before {
  content: "\eefd";
}
.ilmosys-share-oncloud:before {
  content: "\eefe";
}
.ilmosys-share-window:before {
  content: "\eeff";
}
.ilmosys-shark:before {
  content: "\ef00";
}
.ilmosys-sheep:before {
  content: "\ef01";
}
.ilmosys-sheriff-badge:before {
  content: "\ef02";
}
.ilmosys-shield:before {
  content: "\ef03";
}
.ilmosys-ship:before {
  content: "\ef04";
}
.ilmosys-ship-2:before {
  content: "\ef05";
}
.ilmosys-shirt:before {
  content: "\ef06";
}
.ilmosys-shoes:before {
  content: "\ef07";
}
.ilmosys-shoes-2:before {
  content: "\ef08";
}
.ilmosys-shoes-3:before {
  content: "\ef09";
}
.ilmosys-shop:before {
  content: "\ef0a";
}
.ilmosys-shop-2:before {
  content: "\ef0b";
}
.ilmosys-shop-3:before {
  content: "\ef0c";
}
.ilmosys-shop-4:before {
  content: "\ef0d";
}
.ilmosys-shopping-bag:before {
  content: "\ef0e";
}
.ilmosys-shopping-basket:before {
  content: "\ef0f";
}
.ilmosys-shopping-cart:before {
  content: "\ef10";
}
.ilmosys-short-pants:before {
  content: "\ef11";
}
.ilmosys-shovel:before {
  content: "\ef12";
}
.ilmosys-shuffle:before {
  content: "\ef13";
}
.ilmosys-shuffle-2:before {
  content: "\ef14";
}
.ilmosys-shuffle-3:before {
  content: "\ef15";
}
.ilmosys-shuffle-4:before {
  content: "\ef16";
}
.ilmosys-shutter:before {
  content: "\ef17";
}
.ilmosys-sidebar-window:before {
  content: "\ef18";
}
.ilmosys-signal:before {
  content: "\ef19";
}
.ilmosys-singapore:before {
  content: "\ef1a";
}
.ilmosys-skateboard:before {
  content: "\ef1b";
}
.ilmosys-skateboard-2:before {
  content: "\ef1c";
}
.ilmosys-skate-shoes:before {
  content: "\ef1d";
}
.ilmosys-skeleton:before {
  content: "\ef1e";
}
.ilmosys-ski:before {
  content: "\ef1f";
}
.ilmosys-skirt:before {
  content: "\ef20";
}
.ilmosys-skull:before {
  content: "\ef21";
}
.ilmosys-skydiving:before {
  content: "\ef22";
}
.ilmosys-sled:before {
  content: "\ef23";
}
.ilmosys-sled-withgifts:before {
  content: "\ef24";
}
.ilmosys-sleeping:before {
  content: "\ef25";
}
.ilmosys-sleet:before {
  content: "\ef26";
}
.ilmosys-slippers:before {
  content: "\ef27";
}
.ilmosys-smart:before {
  content: "\ef28";
}
.ilmosys-smartphone:before {
  content: "\ef29";
}
.ilmosys-smartphone-2:before {
  content: "\ef2a";
}
.ilmosys-smartphone-3:before {
  content: "\ef2b";
}
.ilmosys-smartphone-4:before {
  content: "\ef2c";
}
.ilmosys-smartphone-secure:before {
  content: "\ef2d";
}
.ilmosys-smile:before {
  content: "\ef2e";
}
.ilmosys-smoking-area:before {
  content: "\ef2f";
}
.ilmosys-smoking-pipe:before {
  content: "\ef30";
}
.ilmosys-snake:before {
  content: "\ef31";
}
.ilmosys-snorkel:before {
  content: "\ef32";
}
.ilmosys-snow:before {
  content: "\ef33";
}
.ilmosys-snow-2:before {
  content: "\ef34";
}
.ilmosys-snow-dome:before {
  content: "\ef35";
}
.ilmosys-snowflake:before {
  content: "\ef36";
}
.ilmosys-snowflake-2:before {
  content: "\ef37";
}
.ilmosys-snowflake-3:before {
  content: "\ef38";
}
.ilmosys-snowflake-4:before {
  content: "\ef39";
}
.ilmosys-snowman:before {
  content: "\ef3a";
}
.ilmosys-snow-storm:before {
  content: "\ef3b";
}
.ilmosys-soccer-ball:before {
  content: "\ef3c";
}
.ilmosys-soccer-shoes:before {
  content: "\ef3d";
}
.ilmosys-socks:before {
  content: "\ef3e";
}
.ilmosys-solar:before {
  content: "\ef3f";
}
.ilmosys-sound:before {
  content: "\ef40";
}
.ilmosys-sound-wave:before {
  content: "\ef41";
}
.ilmosys-soup:before {
  content: "\ef42";
}
.ilmosys-south-africa:before {
  content: "\ef43";
}
.ilmosys-space-needle:before {
  content: "\ef44";
}
.ilmosys-spain:before {
  content: "\ef45";
}
.ilmosys-spam-mail:before {
  content: "\ef46";
}
.ilmosys-speach-bubble:before {
  content: "\ef47";
}
.ilmosys-speach-bubble2:before {
  content: "\ef48";
}
.ilmosys-speach-bubble3:before {
  content: "\ef49";
}
.ilmosys-speach-bubble4:before {
  content: "\ef4a";
}
.ilmosys-speach-bubble5:before {
  content: "\ef4b";
}
.ilmosys-speach-bubble6:before {
  content: "\ef4c";
}
.ilmosys-speach-bubble7:before {
  content: "\ef4d";
}
.ilmosys-speach-bubble8:before {
  content: "\ef4e";
}
.ilmosys-speach-bubble9:before {
  content: "\ef4f";
}
.ilmosys-speach-bubble10:before {
  content: "\ef50";
}
.ilmosys-speach-bubble11:before {
  content: "\ef51";
}
.ilmosys-speach-bubble12:before {
  content: "\ef52";
}
.ilmosys-speach-bubble13:before {
  content: "\ef53";
}
.ilmosys-speach-bubbleasking:before {
  content: "\ef54";
}
.ilmosys-speach-bubblecomic:before {
  content: "\ef55";
}
.ilmosys-speach-bubblecomic2:before {
  content: "\ef56";
}
.ilmosys-speach-bubblecomic3:before {
  content: "\ef57";
}
.ilmosys-speach-bubblecomic4:before {
  content: "\ef58";
}
.ilmosys-speach-bubbledialog:before {
  content: "\ef59";
}
.ilmosys-speach-bubbles:before {
  content: "\ef5a";
}
.ilmosys-speak:before {
  content: "\ef5b";
}
.ilmosys-speak-2:before {
  content: "\ef5c";
}
.ilmosys-speaker:before {
  content: "\ef5d";
}
.ilmosys-speaker-2:before {
  content: "\ef5e";
}
.ilmosys-spell-check:before {
  content: "\ef5f";
}
.ilmosys-spell-checkabc:before {
  content: "\ef60";
}
.ilmosys-spermium:before {
  content: "\ef61";
}
.ilmosys-spider:before {
  content: "\ef62";
}
.ilmosys-spiderweb:before {
  content: "\ef63";
}
.ilmosys-split-foursquarewindow:before {
  content: "\ef64";
}
.ilmosys-split-horizontal:before {
  content: "\ef65";
}
.ilmosys-split-horizontal2window:before {
  content: "\ef66";
}
.ilmosys-split-vertical:before {
  content: "\ef67";
}
.ilmosys-split-vertical2:before {
  content: "\ef68";
}
.ilmosys-split-window:before {
  content: "\ef69";
}
.ilmosys-spoder:before {
  content: "\ef6a";
}
.ilmosys-spoon:before {
  content: "\ef6b";
}
.ilmosys-sport-mode:before {
  content: "\ef6c";
}
.ilmosys-sports-clothings1:before {
  content: "\ef6d";
}
.ilmosys-sports-clothings2:before {
  content: "\ef6e";
}
.ilmosys-sports-shirt:before {
  content: "\ef6f";
}
.ilmosys-spot:before {
  content: "\ef70";
}
.ilmosys-spray:before {
  content: "\ef71";
}
.ilmosys-spread:before {
  content: "\ef72";
}
.ilmosys-spring:before {
  content: "\ef73";
}
.ilmosys-spy:before {
  content: "\ef74";
}
.ilmosys-squirrel:before {
  content: "\ef75";
}
.ilmosys-ssl:before {
  content: "\ef76";
}
.ilmosys-stamp:before {
  content: "\ef77";
}
.ilmosys-stamp-2:before {
  content: "\ef78";
}
.ilmosys-stapler:before {
  content: "\ef79";
}
.ilmosys-star:before {
  content: "\ef7a";
}
.ilmosys-starfish:before {
  content: "\ef7b";
}
.ilmosys-start:before {
  content: "\ef7c";
}
.ilmosys-start-3:before {
  content: "\ef7d";
}
.ilmosys-star-track:before {
  content: "\ef7e";
}
.ilmosys-start-ways:before {
  content: "\ef7f";
}
.ilmosys-statistic:before {
  content: "\ef80";
}
.ilmosys-st-basilscathedral:before {
  content: "\ef81";
}
.ilmosys-stethoscope:before {
  content: "\ef82";
}
.ilmosys-stop:before {
  content: "\ef83";
}
.ilmosys-stop--2:before {
  content: "\ef84";
}
.ilmosys-stop-music:before {
  content: "\ef85";
}
.ilmosys-stopwatch:before {
  content: "\ef86";
}
.ilmosys-stopwatch-2:before {
  content: "\ef87";
}
.ilmosys-storm:before {
  content: "\ef88";
}
.ilmosys-st-paulscathedral:before {
  content: "\ef89";
}
.ilmosys-street-view:before {
  content: "\ef8a";
}
.ilmosys-street-view2:before {
  content: "\ef8b";
}
.ilmosys-strikethrough-text:before {
  content: "\ef8c";
}
.ilmosys-stroller:before {
  content: "\ef8d";
}
.ilmosys-structure:before {
  content: "\ef8e";
}
.ilmosys-student-female:before {
  content: "\ef8f";
}
.ilmosys-student-hat:before {
  content: "\ef90";
}
.ilmosys-student-hat2:before {
  content: "\ef91";
}
.ilmosys-student-male:before {
  content: "\ef92";
}
.ilmosys-student-malefemale:before {
  content: "\ef93";
}
.ilmosys-students:before {
  content: "\ef94";
}
.ilmosys-studio-flash:before {
  content: "\ef95";
}
.ilmosys-studio-lightbox:before {
  content: "\ef96";
}
.ilmosys-suit:before {
  content: "\ef97";
}
.ilmosys-suitcase:before {
  content: "\ef98";
}
.ilmosys-sum:before {
  content: "\ef99";
}
.ilmosys-sum-2:before {
  content: "\ef9a";
}
.ilmosys-summer:before {
  content: "\ef9b";
}
.ilmosys-sun:before {
  content: "\ef9c";
}
.ilmosys-sun-cloudyrain:before {
  content: "\ef9d";
}
.ilmosys-sunglasses:before {
  content: "\ef9e";
}
.ilmosys-sunglasses-2:before {
  content: "\ef9f";
}
.ilmosys-sunglasses-3:before {
  content: "\efa0";
}
.ilmosys-sunglasses-smiley:before {
  content: "\efa1";
}
.ilmosys-sunglasses-smiley2:before {
  content: "\efa2";
}
.ilmosys-sunglasses-w:before {
  content: "\efa3";
}
.ilmosys-sunglasses-w2:before {
  content: "\efa4";
}
.ilmosys-sunglasses-w3:before {
  content: "\efa5";
}
.ilmosys-sunrise:before {
  content: "\efa6";
}
.ilmosys-sunset:before {
  content: "\efa7";
}
.ilmosys-superman:before {
  content: "\efa8";
}
.ilmosys-support:before {
  content: "\efa9";
}
.ilmosys-surprise:before {
  content: "\efaa";
}
.ilmosys-sushi:before {
  content: "\efab";
}
.ilmosys-sweden:before {
  content: "\efac";
}
.ilmosys-swimming:before {
  content: "\efad";
}
.ilmosys-swimming-short:before {
  content: "\efae";
}
.ilmosys-swimmwear:before {
  content: "\efaf";
}
.ilmosys-switch:before {
  content: "\efb0";
}
.ilmosys-switzerland:before {
  content: "\efb1";
}
.ilmosys-sync:before {
  content: "\efb2";
}
.ilmosys-sync-cloud:before {
  content: "\efb3";
}
.ilmosys-synchronize:before {
  content: "\efb4";
}
.ilmosys-synchronize-2:before {
  content: "\efb5";
}
.ilmosys-tablet:before {
  content: "\efb6";
}
.ilmosys-tablet-2:before {
  content: "\efb7";
}
.ilmosys-tablet-3:before {
  content: "\efb8";
}
.ilmosys-tablet-orientation:before {
  content: "\efb9";
}
.ilmosys-tablet-phone:before {
  content: "\efba";
}
.ilmosys-tablet-secure:before {
  content: "\efbb";
}
.ilmosys-tablet-vertical:before {
  content: "\efbc";
}
.ilmosys-tactic:before {
  content: "\efbd";
}
.ilmosys-tag:before {
  content: "\efbe";
}
.ilmosys-tag-2:before {
  content: "\efbf";
}
.ilmosys-tag-3:before {
  content: "\efc0";
}
.ilmosys-tag-4:before {
  content: "\efc1";
}
.ilmosys-tag-5:before {
  content: "\efc2";
}
.ilmosys-taj-mahal:before {
  content: "\efc3";
}
.ilmosys-talk-man:before {
  content: "\efc4";
}
.ilmosys-tap:before {
  content: "\efc5";
}
.ilmosys-target:before {
  content: "\efc6";
}
.ilmosys-target-market:before {
  content: "\efc7";
}
.ilmosys-taurus:before {
  content: "\efc8";
}
.ilmosys-taurus-2:before {
  content: "\efc9";
}
.ilmosys-taxi:before {
  content: "\efca";
}
.ilmosys-taxi-2:before {
  content: "\efcb";
}
.ilmosys-taxi-sign:before {
  content: "\efcc";
}
.ilmosys-teacher:before {
  content: "\efcd";
}
.ilmosys-teapot:before {
  content: "\efce";
}
.ilmosys-teddy-bear:before {
  content: "\efcf";
}
.ilmosys-tee-mug:before {
  content: "\efd0";
}
.ilmosys-telephone:before {
  content: "\efd1";
}
.ilmosys-telephone-2:before {
  content: "\efd2";
}
.ilmosys-telescope:before {
  content: "\efd3";
}
.ilmosys-temperature:before {
  content: "\efd4";
}
.ilmosys-temperature-2:before {
  content: "\efd5";
}
.ilmosys-temperature-3:before {
  content: "\efd6";
}
.ilmosys-temple:before {
  content: "\efd7";
}
.ilmosys-tennis:before {
  content: "\efd8";
}
.ilmosys-tennis-ball:before {
  content: "\efd9";
}
.ilmosys-tent:before {
  content: "\efda";
}
.ilmosys-testimonal:before {
  content: "\efdb";
}
.ilmosys-test-tube:before {
  content: "\efdc";
}
.ilmosys-test-tube2:before {
  content: "\efdd";
}
.ilmosys-text-box:before {
  content: "\efde";
}
.ilmosys-text-effect:before {
  content: "\efdf";
}
.ilmosys-text-highlightcolor:before {
  content: "\efe0";
}
.ilmosys-text-paragraph:before {
  content: "\efe1";
}
.ilmosys-thailand:before {
  content: "\efe2";
}
.ilmosys-the-whitehouse:before {
  content: "\efe3";
}
.ilmosys-this-sideup:before {
  content: "\efe4";
}
.ilmosys-thread:before {
  content: "\efe5";
}
.ilmosys-three-arrowfork:before {
  content: "\efe6";
}
.ilmosys-three-fingers:before {
  content: "\efe7";
}
.ilmosys-three-fingersdrag:before {
  content: "\efe8";
}
.ilmosys-three-fingersdrag2:before {
  content: "\efe9";
}
.ilmosys-three-fingerstouch:before {
  content: "\efea";
}
.ilmosys-thumb:before {
  content: "\efeb";
}
.ilmosys-thumbs-downsmiley:before {
  content: "\efec";
}
.ilmosys-thumbs-upsmiley:before {
  content: "\efed";
}
.ilmosys-thunder:before {
  content: "\efee";
}
.ilmosys-thunderstorm:before {
  content: "\efef";
}
.ilmosys-ticket:before {
  content: "\eff0";
}
.ilmosys-tie:before {
  content: "\eff1";
}
.ilmosys-tie-2:before {
  content: "\eff2";
}
.ilmosys-tie-3:before {
  content: "\eff3";
}
.ilmosys-tie-4:before {
  content: "\eff4";
}
.ilmosys-tiger:before {
  content: "\eff5";
}
.ilmosys-time-backup:before {
  content: "\eff6";
}
.ilmosys-time-bomb:before {
  content: "\eff7";
}
.ilmosys-time-clock:before {
  content: "\eff8";
}
.ilmosys-time-fire:before {
  content: "\eff9";
}
.ilmosys-time-machine:before {
  content: "\effa";
}
.ilmosys-timer:before {
  content: "\effb";
}
.ilmosys-timer-2:before {
  content: "\effc";
}
.ilmosys-time-window:before {
  content: "\effd";
}
.ilmosys-to-bottom:before {
  content: "\effe";
}
.ilmosys-to-bottom2:before {
  content: "\efff";
}
.ilmosys-token:before {
  content: "\f000";
}
.ilmosys-to-left:before {
  content: "\f001";
}
.ilmosys-tomato:before {
  content: "\f002";
}
.ilmosys-tongue:before {
  content: "\f003";
}
.ilmosys-tooth:before {
  content: "\f004";
}
.ilmosys-tooth-2:before {
  content: "\f005";
}
.ilmosys-top-tobottom:before {
  content: "\f006";
}
.ilmosys-to-right:before {
  content: "\f007";
}
.ilmosys-to-top:before {
  content: "\f008";
}
.ilmosys-to-top2:before {
  content: "\f009";
}
.ilmosys-touch-window:before {
  content: "\f00a";
}
.ilmosys-tourch:before {
  content: "\f00b";
}
.ilmosys-tower:before {
  content: "\f00c";
}
.ilmosys-tower-2:before {
  content: "\f00d";
}
.ilmosys-tower-bridge:before {
  content: "\f00e";
}
.ilmosys-trace:before {
  content: "\f00f";
}
.ilmosys-tractor:before {
  content: "\f010";
}
.ilmosys-traffic-light:before {
  content: "\f011";
}
.ilmosys-traffic-light2:before {
  content: "\f012";
}
.ilmosys-train:before {
  content: "\f013";
}
.ilmosys-train-2:before {
  content: "\f014";
}
.ilmosys-tram:before {
  content: "\f015";
}
.ilmosys-transform:before {
  content: "\f016";
}
.ilmosys-transform-2:before {
  content: "\f017";
}
.ilmosys-transform-3:before {
  content: "\f018";
}
.ilmosys-transform-4:before {
  content: "\f019";
}
.ilmosys-trash-withmen:before {
  content: "\f01a";
}
.ilmosys-tree:before {
  content: "\f01b";
}
.ilmosys-tree-2:before {
  content: "\f01c";
}
.ilmosys-tree-3:before {
  content: "\f01d";
}
.ilmosys-tree-4:before {
  content: "\f01e";
}
.ilmosys-tree-5:before {
  content: "\f01f";
}
.ilmosys-trekking:before {
  content: "\f020";
}
.ilmosys-triangle-arrowdown:before {
  content: "\f021";
}
.ilmosys-triangle-arrowleft:before {
  content: "\f022";
}
.ilmosys-triangle-arrowright:before {
  content: "\f023";
}
.ilmosys-triangle-arrowup:before {
  content: "\f024";
}
.ilmosys-tripod-2:before {
  content: "\f025";
}
.ilmosys-tripod-andvideo:before {
  content: "\f026";
}
.ilmosys-tripod-withcamera:before {
  content: "\f027";
}
.ilmosys-tripod-withgopro:before {
  content: "\f028";
}
.ilmosys-trophy:before {
  content: "\f029";
}
.ilmosys-trophy-2:before {
  content: "\f02a";
}
.ilmosys-truck:before {
  content: "\f02b";
}
.ilmosys-trumpet:before {
  content: "\f02c";
}
.ilmosys-t-shirt:before {
  content: "\f02d";
}
.ilmosys-turkey:before {
  content: "\f02e";
}
.ilmosys-turn-down:before {
  content: "\f02f";
}
.ilmosys-turn-down2:before {
  content: "\f030";
}
.ilmosys-turn-downfromleft:before {
  content: "\f031";
}
.ilmosys-turn-downfromright:before {
  content: "\f032";
}
.ilmosys-turn-left:before {
  content: "\f033";
}
.ilmosys-turn-left3:before {
  content: "\f034";
}
.ilmosys-turn-right:before {
  content: "\f035";
}
.ilmosys-turn-right3:before {
  content: "\f036";
}
.ilmosys-turn-up:before {
  content: "\f037";
}
.ilmosys-turn-up2:before {
  content: "\f038";
}
.ilmosys-turtle:before {
  content: "\f039";
}
.ilmosys-tuxedo:before {
  content: "\f03a";
}
.ilmosys-tv:before {
  content: "\f03b";
}
.ilmosys-twister:before {
  content: "\f03c";
}
.ilmosys-two-fingers:before {
  content: "\f03d";
}
.ilmosys-two-fingersdrag:before {
  content: "\f03e";
}
.ilmosys-two-fingersdrag2:before {
  content: "\f03f";
}
.ilmosys-two-fingersscroll:before {
  content: "\f040";
}
.ilmosys-two-fingerstouch:before {
  content: "\f041";
}
.ilmosys-two-windows:before {
  content: "\f042";
}
.ilmosys-type-pass:before {
  content: "\f043";
}
.ilmosys-ukraine:before {
  content: "\f044";
}
.ilmosys-umbrela:before {
  content: "\f045";
}
.ilmosys-umbrella-2:before {
  content: "\f046";
}
.ilmosys-umbrella-3:before {
  content: "\f047";
}
.ilmosys-under-linetext:before {
  content: "\f048";
}
.ilmosys-undo:before {
  content: "\f049";
}
.ilmosys-united-kingdom:before {
  content: "\f04a";
}
.ilmosys-united-states:before {
  content: "\f04b";
}
.ilmosys-university:before {
  content: "\f04c";
}
.ilmosys-university-2:before {
  content: "\f04d";
}
.ilmosys-unlock:before {
  content: "\f04e";
}
.ilmosys-unlock-2:before {
  content: "\f04f";
}
.ilmosys-unlock-3:before {
  content: "\f050";
}
.ilmosys-up:before {
  content: "\f051";
}
.ilmosys-up-2:before {
  content: "\f052";
}
.ilmosys-up-3:before {
  content: "\f053";
}
.ilmosys-up-4:before {
  content: "\f054";
}
.ilmosys-up--down:before {
  content: "\f055";
}
.ilmosys-up--down3:before {
  content: "\f056";
}
.ilmosys-upgrade:before {
  content: "\f057";
}
.ilmosys-upload:before {
  content: "\f058";
}
.ilmosys-upload-2:before {
  content: "\f059";
}
.ilmosys-upload-tocloud:before {
  content: "\f05a";
}
.ilmosys-upload-window:before {
  content: "\f05b";
}
.ilmosys-uppercase-text:before {
  content: "\f05c";
}
.ilmosys-upward:before {
  content: "\f05d";
}
.ilmosys-url-window:before {
  content: "\f05e";
}
.ilmosys-usb:before {
  content: "\f05f";
}
.ilmosys-usb-2:before {
  content: "\f060";
}
.ilmosys-usb-cable:before {
  content: "\f061";
}
.ilmosys-user:before {
  content: "\f062";
}
.ilmosys-vase:before {
  content: "\f063";
}
.ilmosys-vector:before {
  content: "\f064";
}
.ilmosys-vector-2:before {
  content: "\f065";
}
.ilmosys-vector-3:before {
  content: "\f066";
}
.ilmosys-vector-4:before {
  content: "\f067";
}
.ilmosys-vector-5:before {
  content: "\f068";
}
.ilmosys-venn-diagram:before {
  content: "\f069";
}
.ilmosys-vest:before {
  content: "\f06a";
}
.ilmosys-vest-2:before {
  content: "\f06b";
}
.ilmosys-video:before {
  content: "\f06c";
}
.ilmosys-video-2:before {
  content: "\f06d";
}
.ilmosys-video-3:before {
  content: "\f06e";
}
.ilmosys-video-4:before {
  content: "\f06f";
}
.ilmosys-video-5:before {
  content: "\f070";
}
.ilmosys-video-6:before {
  content: "\f071";
}
.ilmosys-video-gamecontroller:before {
  content: "\f072";
}
.ilmosys-video-len:before {
  content: "\f073";
}
.ilmosys-video-len2:before {
  content: "\f074";
}
.ilmosys-video-photographer:before {
  content: "\f075";
}
.ilmosys-video-tripod:before {
  content: "\f076";
}
.ilmosys-vietnam:before {
  content: "\f077";
}
.ilmosys-view-height:before {
  content: "\f078";
}
.ilmosys-view-width:before {
  content: "\f079";
}
.ilmosys-virgo:before {
  content: "\f07a";
}
.ilmosys-virgo-2:before {
  content: "\f07b";
}
.ilmosys-virus:before {
  content: "\f07c";
}
.ilmosys-virus-2:before {
  content: "\f07d";
}
.ilmosys-virus-3:before {
  content: "\f07e";
}
.ilmosys-visa:before {
  content: "\f07f";
}
.ilmosys-voice:before {
  content: "\f080";
}
.ilmosys-voicemail:before {
  content: "\f081";
}
.ilmosys-volleyball:before {
  content: "\f082";
}
.ilmosys-volume-down:before {
  content: "\f083";
}
.ilmosys-volume-up:before {
  content: "\f084";
}
.ilmosys-vpn:before {
  content: "\f085";
}
.ilmosys-wacom-tablet:before {
  content: "\f086";
}
.ilmosys-waiter:before {
  content: "\f087";
}
.ilmosys-walkie-talkie:before {
  content: "\f088";
}
.ilmosys-wallet:before {
  content: "\f089";
}
.ilmosys-wallet-2:before {
  content: "\f08a";
}
.ilmosys-wallet-3:before {
  content: "\f08b";
}
.ilmosys-warehouse:before {
  content: "\f08c";
}
.ilmosys-warning-window:before {
  content: "\f08d";
}
.ilmosys-watch:before {
  content: "\f08e";
}
.ilmosys-watch-2:before {
  content: "\f08f";
}
.ilmosys-watch-3:before {
  content: "\f090";
}
.ilmosys-wave:before {
  content: "\f091";
}
.ilmosys-wave-2:before {
  content: "\f092";
}
.ilmosys-webcam:before {
  content: "\f093";
}
.ilmosys-weight-lift:before {
  content: "\f094";
}
.ilmosys-wheelbarrow:before {
  content: "\f095";
}
.ilmosys-wheelchair:before {
  content: "\f096";
}
.ilmosys-width-window:before {
  content: "\f097";
}
.ilmosys-wifi:before {
  content: "\f098";
}
.ilmosys-wifi-2:before {
  content: "\f099";
}
.ilmosys-wifi-keyboard:before {
  content: "\f09a";
}
.ilmosys-windmill:before {
  content: "\f09b";
}
.ilmosys-window:before {
  content: "\f09c";
}
.ilmosys-window-2:before {
  content: "\f09d";
}
.ilmosys-windows:before {
  content: "\f09e";
}
.ilmosys-windows-2:before {
  content: "\f09f";
}
.ilmosys-windsock:before {
  content: "\f0a0";
}
.ilmosys-wind-turbine:before {
  content: "\f0a1";
}
.ilmosys-windy:before {
  content: "\f0a2";
}
.ilmosys-wine-bottle:before {
  content: "\f0a3";
}
.ilmosys-wine-glass:before {
  content: "\f0a4";
}
.ilmosys-wink:before {
  content: "\f0a5";
}
.ilmosys-winter:before {
  content: "\f0a6";
}
.ilmosys-winter-2:before {
  content: "\f0a7";
}
.ilmosys-wireless:before {
  content: "\f0a8";
}
.ilmosys-witch:before {
  content: "\f0a9";
}
.ilmosys-witch-hat:before {
  content: "\f0aa";
}
.ilmosys-wizard:before {
  content: "\f0ab";
}
.ilmosys-wolf:before {
  content: "\f0ac";
}
.ilmosys-womanman:before {
  content: "\f0ad";
}
.ilmosys-woman-sign:before {
  content: "\f0ae";
}
.ilmosys-womans-underwear:before {
  content: "\f0af";
}
.ilmosys-womans-underwear2:before {
  content: "\f0b0";
}
.ilmosys-women:before {
  content: "\f0b1";
}
.ilmosys-wonder-woman:before {
  content: "\f0b2";
}
.ilmosys-worker:before {
  content: "\f0b3";
}
.ilmosys-worker-clothes:before {
  content: "\f0b4";
}
.ilmosys-wrap-text:before {
  content: "\f0b5";
}
.ilmosys-wreath:before {
  content: "\f0b6";
}
.ilmosys-wrench:before {
  content: "\f0b7";
}
.ilmosys-x-ray:before {
  content: "\f0b8";
}
.ilmosys-yacht:before {
  content: "\f0b9";
}
.ilmosys-yes:before {
  content: "\f0ba";
}
.ilmosys-ying-yang:before {
  content: "\f0bb";
}
.ilmosys-z-a:before {
  content: "\f0bc";
}
.ilmosys-zebra:before {
  content: "\f0bd";
}
.ilmosys-zombie:before {
  content: "\f0be";
}
.ilmosys-zoom-gesture:before {
  content: "\f0bf";
}

