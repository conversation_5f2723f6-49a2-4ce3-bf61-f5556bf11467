/*------------------------------------------------------------------
Template Name: Foodera - Responsive Landing Page Template
Version: 1.0
Author: Thememor
Author URL: https://themeforest.net/user/thememor
-------------------------------------------------------------------*/

/*------------------------------------------------------------------
[TABLE OF CONTENTS]

	+ GLOBAL STYLES
	+ COMMON STYLES 
	+ HEADER STYLES 
	+ INTRO STYLES
	+ ICON BOX / SERVICES STYLES 
	+ INFO STYLES 
	+ TESTIMONIAL STYLES 
	+ ABOUT   
	+ FEATURES 
	+ PRICING TABLE  
	+ CLIENTS 
	+ CONTACT 
	+ MAILCHIMP 
	+ ELEMENTS 
	+ FOOTER 
	+ RESPONSIVE STYLES 

-------------------------------------------------------------------*/

@import url('https://fonts.googleapis.com/css?family=Quicksand:300,400,500,700&display=swap');
@import url('https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i&display=swap');

/* GLOBAL STYLES */

body {
	background: #fff;
	font-family: 'Roboto', sans-serif;
	color: #555;
	font-weight: 400;
	font-size: 16px;
	letter-spacing: 0.5px;
}

h1,h2,h3,h4,h5,h6 {
	color: #43121d;
	font-family: 'Quicksand', sans-serif;
	font-weight: 700;
	margin: 0;
	letter-spacing: 0.1px;
}

p {
	font-size: 16px;
	color: #555;
	line-height: 28px;
	font-weight: 400;
	font-family: 'Roboto', sans-serif;
	letter-spacing: 0.5px;
}

b, strong {
	font-weight: 700;
}

a {
	color: #43121d;
}

a:hover {
	color: #a82d49;
}

a, a:hover, a:focus, button, button:hover {
	outline: 0;
	text-decoration: none;
	transition: .4s;
}

ul, ol, li {
	margin: 0;
	padding: 0;
	list-style: none;
}

.space10 {
	margin-bottom: 10px;
}

.space20 {
	margin-bottom: 20px;
}

.space30 {
	margin-bottom: 30px;
}

.space40 {
	margin-bottom: 40px;
}

.space50 {
	margin-bottom: 50px;
}

.space60 {
	margin-bottom: 60px;
}

.space70 {
	margin-bottom: 70px;
}

.space80 {
	margin-bottom: 80px;
}

.space90 {
	margin-bottom: 90px;
}

.space100 {
	margin-bottom: 100px;
}

.body {
	position: relative;
	width: 100%;
	overflow: hidden;
}

.boxed {
	background: #262626 url(../images/bg/pattern.png) repeat fixed;
}

.boxed .body {
	background: #fff;
	position: relative;
	width: 90%;
	margin: 0 auto;
	max-width: 1280px;
	overflow: hidden;
	box-shadow: 0 5px 25px rgba(0,0,0,0.2);
}

.boxed .navbar-fixed-top {
	width: 90%;
	margin: 0 auto;
	max-width: 1280px;
}

.bg-light{
	background-color: #fafafa;
}

/* COMMON STYLES */

.video {
	position: relative;
	padding-bottom: 56.25%;
	height: 0;
	overflow: hidden;
}

.video iframe,
.video object,  
.video embed {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: none;
}

.no-padding {
	padding: 0px !important;
}

.no-padding-bottom {
	padding-bottom: 0px !important;
}
.tc h4{
	text-align:center !important;
}
.tc p{
	text-align:center !important;
}


/* HEADER STYLES */

.navbar-fixed-top {
	position: fixed;
	right: 0;
	left: 0;
	font-family: 'Roboto', sans-serif;
	border-width: 0;
	border-radius: 0;
	background: transparent;
}

.navbar-inverse {
	transition: .4s;
}

.navbar {
	min-height: 86px;
	margin-bottom: 0;
	border: none;
}

.navbar-lg .navbar-brand {
	padding: 5px 0 0;
	font-size: 28px;
	font-weight: 700;
	text-transform: uppercase;
	color: #fff;
	letter-spacing: 5px;
}

.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
	color: #fff;
	background-color: transparent;
}

.navbar-lg .navbar-nav > li > a {
	padding: 40px 12px 35px;
	font-size: 15px;
	line-height: 1.6;
	color: #a82d49;
	font-weight: 600;
}
.navbar-lg .navbar-nav > li > a:hover {
	color: #a82d49;
}

.navbar-login {
	margin-top: 6px;
	margin-left: 10px;
}

.navbar-login a {
	background: #a82d49;
	color: #fff !important;
	border: 3px solid #a82d49;
	padding: 5px 23px !important;
	font-weight: 800;
	letter-spacing: 0.2px;
	margin-top: 25px;
}

.navbar-login a:hover {
	border: 3px solid #a82d49 !important;
	background: transparent;
	color: #a82d49 !important;
}

.dropdown-menu {
	display: block !important;
	padding: 0;
	margin: 0;
	background: #2F2D2D;
	top: 65px;
	left: 0 !important;
	right: auto !important;
	opacity: 0;
	visibility: hidden;
	transition: .4s;
	
}

.dropdown-menu li a:after {
	display: none;
}

.navbar-nav > li:hover .dropdown-menu {
	opacity: 1;
	visibility: visible;
	transition: .4s;
	border-radius: 4px;
}

.dropdown-menu > li > a {
	padding: 12px 20px;
	color: #fff;
	text-transform: none;
	font-size: 11px;
	font-weight: 400;
	letter-spacing: 0.2px;
	width: 100%;
}

.navbar-nav > li > .dropdown-menu {
	margin-top: 14px;
	margin-left: 13px;
	border: none;
}

.dropdown-menu > li > a:hover {
	color: #a82d49;
	background: transparent;
}

.mega-menu {
	width: 100%;
	position: absolute;
	left: 15px !important;
	right: 0px !important;
	top: 79px;
	max-width: 1155px;
	margin: 0 auto !important;
	float: none;
	padding: 20px 0;
}

.mm-menu ,
.navbar-right {
	position: static !important;
}

.mm-menu .dropdown-menu > li > a {
	border-bottom: none;
}

.dropdown-menu > li > a {
	text-transform: uppercase;
}

.section-head {
	position: relative;
	z-index: 55;
}

.section-head h1 {
	text-transform: uppercase;
	font-weight: 700;
	margin: 0 0 15px;
	font-size: 25px;
	letter-spacing: 3px;
}

.section-head-lite h1 {
	font-weight: 700;
	margin: 0 0 15px;
	font-size: 36px;
	color: #fff;
}

.section-head-lite p {
	color: #fff;
	opacity: 0.7;
}

/* INTRO STYLES */

.intro {
	position: relative;
}

.intro .container,
.intro img {
	position: relative;
	z-index: 77;
	border-radius: 10px;
}

.overlay {
	background: #151515;
	opacity: 0.5;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
}

/*  Intro */

.intro3 {
	background: url(../images/bg/3.jpg) no-repeat center;
	padding: 270px 0 150px;
	background-size: cover;
}

.intro3 h3 {
	color: #43121d;
	margin: 0px 0px 25px;
	font-size: 48px;
	letter-spacing: 0;
}

.intro3 p {
	font-size: 16px;
	font-weight: 400;
	color: #555555;
	padding: 0 0 13px;
}

.intro-newsletter {
	width: 55%;
	margin: 0px auto;
}

.intro-newsletter p {
	margin: 0;
	font-size: 13px;
	color: #fff;
	opacity: 0.7;
	padding: 15px 0 0;
	display: table;
	width: 100%;
	letter-spacing: 0.2px;
	font-weight: 400;
}

.intro-newsletter input {
	height: 50px;
	padding: 0 25px;
	font-size: 16px;
	letter-spacing: 0.04em;
	font-weight: 400;
	width: 98%;
	border: none;
	background: transparent;
	border: 2px solid #ddd;
	color: #000;
	border-bottom-left-radius: 20px;
	border-bottom-right-radius: 20px;
}

.intro-newsletter button {
	padding: 0;
	height: 50px;
	font-size: 14px;
}

.intro-newsletter-full {
	width: 100%;
	padding: 0 15px;
}

.intro-newsletter-full p {
	margin: 0;
	font-size: 11px;
	color: #fff;
	opacity: 0.7;
	padding: 8px 0 0;
	display: table;
	width: 100%;
	letter-spacing: 0.4px;
	font-weight: 400;
	margin: 0;
}

.intro-benifits {
	background: #48c7fe;
	background-size: cover;
}
.benifits h3{
	font-size: 28px;
	line-height:45px;
	color:#fff;
	font-weight:700;
}
.benifits p{
	color:#fff;
	font-size: 16px;
	line-height:26px;
}
.benifits p span{
	font-size: 48px;
	font-family: 'Roboto';
	font-weight:100;
	vertical-align:-moz-middle-with-baseline;
}
.intro-about2 {
	padding-top:100px;
	background: #fff;
	background-size: cover;
}
.intro-about2 h3 {
	font-size: 32px;
	line-height:45px;
	color:#43121d;
	font-weight:700;
}
.intro-about2 p{
	padding-top:20px;
	padding-bottom:20px;
}


.hl-container {
	width: 100%;
	max-width: 520px;
	position: relative;
}

.hl-container .hl-image {
	width: 100%;
}

.trigger {
	position: relative;
	z-index: 2;
	display: block;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: #3F51B5;
	-webkit-transition: background-color 0.2s;
	-moz-transition: background-color 0.2s;
	transition: background-color 0.2s;
	cursor: pointer;
}

.trigger:after, .trigger:before {
	content: '';
	position: absolute;
	left: 50%;
	top: 50%;
	bottom: auto;
	right: auto;
	-webkit-transform: translateX(-50%) translateY(-50%);
	-moz-transform: translateX(-50%) translateY(-50%);
	-ms-transform: translateX(-50%) translateY(-50%);
	-o-transform: translateX(-50%) translateY(-50%);
	transform: translateX(-50%) translateY(-50%);
	background-color: #ffffff;
	-webkit-transition-property: -webkit-transform;
	-moz-transition-property: -moz-transform;
	transition-property: transform;
	-webkit-transition-duration: 0.2s;
	-moz-transition-duration: 0.2s;
	transition-duration: 0.2s;
}

.trigger:after {
	height: 2px;
	width: 12px;
}

.trigger:before {
	height: 12px;
	width: 2px;
}

.trigger-wrap {
	width: 30px;
	height: 30px;
	position: relative;
	border-radius: 50%;
}

.trigger-wrap:after {
	content: '';
	position: absolute;
	z-index: 1;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	border-radius: inherit;
	background-color: transparent;
	-webkit-animation: cd-pulse 2s infinite;
	-moz-animation: cd-pulse 2s infinite;
	animation: cd-pulse 2s infinite;
}

.h1-point-info {
	position: absolute;
	width: 350px;
	left: -175px;
	margin-top: 35px;
	background: #fff;
	color: #000;
	padding: 25px;
	border-radius: 5px;
	z-index: 444;
	webkit-box-shadow: 0px 40px 80px -40px rgba(0,0,0,1) !important;
	-moz-box-shadow: 0px 40px 80px -40px rgba(0,0,0,1) !important;
	box-shadow: 0px 40px 80px -40px rgba(0,0,0,1) !important;
	visibility: hidden;
	opacity: 0;
	transition: .3s;
}

.h1-point-info.active {
	margin-top: 15px;
	visibility: visible;
	opacity: 1;
	transition: .6s;
}

.h1-point-info h6 {
	font-size: 16px;
	text-transform: uppercase;
	color: #000;
	font-weight: 700;
	letter-spacing: 0.2px;
}

.h1-point-info p {
	color: #555;
	font-size: 13px;
	line-height: 22px;
	padding: 15px 0 0;
	margin: 0;
}

.hl-point {
	position: absolute;
	z-index: 99;
}

.hl-point2 {
	bottom: 38%;
	right: 29%;
}

.hl-point3 {
	bottom: 15%;
	right: 65%;
}

.hl-point1 {
	top: 35%;
	left: 32%;
}

@-webkit-keyframes cd-pulse {
	0% {
		-webkit-transform: scale(1);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	50% {
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	100% {
		-webkit-transform: scale(1.6);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0);
	}
}

@-moz-keyframes cd-pulse {
	0% {
		-moz-transform: scale(1);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	50% {
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	100% {
		-moz-transform: scale(1.6);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0);
	}
}

@keyframes cd-pulse {
	0% {
		-webkit-transform: scale(1);
		-moz-transform: scale(1);
		-ms-transform: scale(1);
		-o-transform: scale(1);
		transform: scale(1);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	50% {
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0.8);
	}

	100% {
		-webkit-transform: scale(1.6);
		-moz-transform: scale(1.6);
		-ms-transform: scale(1.6);
		-o-transform: scale(1.6);
		transform: scale(1.6);
		box-shadow: inset 0 0 1px 1px rgba(63, 81, 181, 0);
	}
}


.btn {
	font-size: 16px;
	line-height: 20px;
	font-weight: bold;
	text-transform:capitalize;
	padding: 11px 28px;
	border: none;
	border-radius: 0px;
	font-family: Roboto;
	webkit-box-shadow: 0px 2px 10px -1px rgba(0, 0, 0, 0.19);
-moz-box-shadow: 0px 2px 10px -1px rgba(0, 0, 0, 0.19);
-ms-box-shadow: 0px 2px 10px -1px rgba(0, 0, 0, 0.19);
-o-box-shadow: 0px 2px 10px -1px rgba(0, 0, 0, 0.19);
box-shadow: 0px 2px 10px -1px rgba(0, 0, 0, 0.19);
}

.btn i {
	margin-left: 10px;
	position: relative;
	top: 1px;
}

.btn .fa {
	margin-left: 12px;
	position: relative;
	top: 0px;
	font-size: 14px;
}

.btn.btn-primary {
	background: #a82d49;
	border: 3px solid #a82d49;
	color: #fff;
}

.btn.btn-primary:hover {
	border: 3px solid #a82d49 !important;
 background: transparent;
 color: #a82d49;
}

.btn.btn-default {
	color: #000;
	background-color: #ffffff;
	border: 3px solid #fff;
}

.btn.btn-default:hover {
	border: 3px solid #a82d49 !important;
    background: transparent;
    color: #a82d49;
}
	
.btn.btn-light:hover {
	border: 3px solid #fff !important;
	background: transparent;
	color: #fff;
}


.btn.btn-dark {
	color: #fff;
	background-color: #262626;
	border: 3px solid #262626;
}

.btn.btn-lg {
	font-size: 14px;
}

.btn-cta {
	padding: 15px !important;
	width: 100%;
	max-width: 300px;
}

.cta-btn {
	font-family: Roboto;
	font-weight: 700;
	font-size: 15px;
	letter-spacing: 0.025em;
	color: #343434333;
	border: 3px solid #43121d;
	text-align: center;
	text-transform: uppercase;
	height: 50px;
	line-height: 45px;
	display: table;
	margin: 3px 0;
	float: right;
	padding: 0 20px;
}

/*  ICON BOX  */

.services {
	padding: 100px 0 0;
}

.service-box {
	text-align: center;
}

.service-box i {
	font-size: 70px;
	color: #a82d49;
}

.service-box h4 {
	margin: 25px 0 20px;
	font-weight: 700;
	text-transform: uppercase;
	font-size: 15px;
	letter-spacing: 0.2px;
}

.service-box p {
	font-size: 14px;
	padding: 0 15px;
}

.icon-box {
	padding: 30px 0;
}

.icon-box::before {
	display: none;
}

.service2 {
	background: #a82d49;
	padding: 100px 0;
}

.s2-box-ico div {
	font-size: 46px;
	line-height: 128px;
	height: 110px;
	width: 110px;
	margin: 0 auto 25px;
	color: #fff;
	border-radius: 50%;
	box-shadow: 0 0 0 2px #fff inset;
	position: relative;
	overflow: hidden;
}

.s2-box-ico div:after {
	content: '';
	display: block;
	position: absolute;
	top: -2px;
	left: 0;
	height: 0;
	width: 100%;
	background-color: #fff;
	transition: .4s;
}

.s2-box:hover .s2-box-ico div:after {
	content: '';
	display: block;
	position: absolute;
	top: -2px;
	left: 0;
	height: 100%;
	width: 100%;
	background-color: #fff;
	transition: .4s;
}

.s2-box:hover .s2-box-ico div i {
	position: relative;
	z-index: 55;
}

.s2-box:hover .s2-box-ico div {
	color: #a82d49;
}

.s2-box h4 {
	margin: 0px 0px 15px;
	font-size: 07px;
	font-weight: 400;
	color: #fff;
}

.s2-box p {
	color: #fff;
	padding: 0 15px;
}

.service3 {
	padding: 0px 0 100px;
}

.icon-box-square .text-center {
	padding: 30px 30px 15px;
	position: relative;
	text-align: left;
	box-shadow: 0px 7px 16px 0px rgba(50, 50, 50, 0.2);
	background-color: #fff;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
}

.icon-box-square .text-center span {
	position: absolute;
	border-bottom-left-radius: 20px;
	border-bottom-right-radius: 20px;
	height: 50px;
    width: 50px;
	top: -25px;
	background: #a82d49;
	color: #fff;
}

.icon-box-square .text-center h2 {
	font-weight: 700;
	font-size: 18px;
	text-transform:capitalize;
	text-align: left;
	letter-spacing: 0.2px;
	margin: 15px 0 15px;
}
.icon-box-square .text-center span i{
	font-size: 24px;
	text-align: center;
	color: #fff;
	padding-left: 12px;
	padding-top: 12px;
}

.icon-box-square .text-center p {
	font-size: 14px;
}

/*  INFO STYLES */

.info-content h3 {
	font-weight: 600;
	letter-spacing:0.2px;
	font-size: 24px;
	margin: 0px 0 20px;
}

.keysize i {
	color:#222;
	font-size: 28px;
	padding-right:11px;
}

.info-content h4 {
	font-weight: 400;
	font-size: 30px;
	margin: 0px 0 20px;
	line-height: 40px;
}

.info-content p {
	margin: 0 0 10px;
}

.list li {
	padding: 6px 0 9px 35px;
	position: relative;
	color: #555;
}

.list li i {
	min-width: 30px;
	color: #a82d49;
	position: absolute;
	left: 0;
	top: 4px;
	font-size: 25px;
}

/* TESTIMONIAL STYLES */

.testimonials {
	background: #070707  no-repeat center;
	background-size: cover;
	margin-top: -70px;
	padding: 160px 0;
}

.testimonials p {
	font-size: 23px;
	line-height: 32px;
	font-weight: 300;
	color: #fff;
	padding: 0 07% 20px;
	letter-spacing: -0.3px;
}

.testimonials .author {
	font-weight: bold;
	font-size: 11px;
	text-transform: uppercase;
	letter-spacing: 0.2px;
	color: #fff;
}

.testimonials i {
	color: #fff;
	font-size: 75px;
	margin: 0px auto 30px;
	display: table;
}

.slick-initialized .slick-slide {
    display: block;
    background: #fff;
    padding: 50px;
    width: 150px;
    border-radius: 15px;
}


/*  ABOUT */

.about-inline {
	padding: 80px 0;
}

.about-inline .ai-slide {
	width: 1080px;
	position: relative;
	margin: 0 auto;
}

.about-inline .ai-slide .center-block {
	width: 1080px;
}

.ai-slide-img {
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
}

.ai-slide-img {
	position: absolute;
	width: 710px;
	overflow: hidden;
	height: 442px;
	margin: 0 auto;
	display: block;
	left: -7px;
	right: 0;
	top: 39px;
}

.ai-slide-img-inner {
	width: 100%;
}

.ai-slide-img img {
	width: 100%;
	float: left;
}

.about-inline h3 {
	color: #43121d;
	margin-bottom: 15px;
	font-size: 36px;
	line-height: 46px;
	font-weight: 700;
}

.about-inline p {
	font-size: 17px;
	padding: 0 14%;
}

.ai-list {
	margin-top: 40px;
}

.ai-list ul li {
	display: inline-block;
	color: #43121d;
	min-width: 160px;
	padding: 0 15px;
	font-weight: 700;
	font-size: 13px;
	line-height: 30px;
	border-left: 1px solid #ddd;
	text-transform: uppercase;
	letter-spacing: 0.2px;
}

.ai-list ul li:first-child {
	border-left: none;
}

.ai-list ul li i {
	display: block;
	margin-bottom: 22px;
	font-size: 60px;
	color: #a82d49;
	transition: .4s;
}

.ai-list ul li:hover i {
	transform: scale(1.2);
	transition: .4s;
}

.ai-list ul li:hover {
	cursor: pointer;
}

.ai-slide-img-inner {
	position: relative;
	left: 0%;
	width: 300%;
}

.ai-slide-img-inner div {
	text-align: center;
	float: left;
	width: 33.33333%;
	overflow: hidden;
}

.ai-slide-img-inner {
	transition: .4s;
}

.ai-slide2-active {
	position: relative;
	left: 0%;
}

.ai-slide2-active {
	position: relative;
	left: -100%;
}

.ai-slide3-active {
	position: relative;
	left: -200%;
}

.info-content2 {
	padding: 50px 0 25px !important;
	background: #a82d49 !important;
}
.info-content2 p{
	font-style:italic;
	font-size: 16px;
}
.info-content2 h3 {
	font-weight: 700;
	font-size: 32px;	
	letter-spacing: 0.2px;
	color: #fff;
    text-align: center;
}

.info-content2 p {
	margin: 0 0 20px;
	color: #fff;
	text-align: center;
}

.info-content2 .list li {
	padding: 9px 0 9px 35px;
	font-size: 15px;
	color: #fff;
	position: relative;
}

.info-content2 .list li i {
	text-align: center;
	color: #fff;
	position: absolute;
	top: 7px;
	font-size: 20px;
}

/* VIDEO BOX */

.video-box {
	position: relative;
	display: table;
	width: 100%;
}

.video-box img {
	border-radius: 3px;
}

.video-box span {
	width: 100px;
	height: 100px;
	background: #fff;
	color: #000;
	border-radius: 50%;
	position: absolute;
	top: 50%;
	line-height: 100px;
	text-align: center;
	font-size: 30px;
	left: 50%;
	webkit-box-shadow: 0px 40px 40px -30px rgba(0,0,0,1) !important;
	-moz-box-shadow: 0px 40px 40px -30px rgba(0,0,0,1) !important;
	box-shadow: 0px 40px 40px -30px rgba(0,0,0,1) !important;
	transform: translate(-50%,-50%);
	transition: .4s;
	-moz-transition: .4s;
	-webkit-transition: .4s;
}

.video-box:hover span {
	width: 105px;
	height: 105px;
	color: #a82d49;
	line-height: 105px;
	font-size: 35px;
	transition: .4s;
	-moz-transition: .4s;
	-webkit-transition: .4s;
}

.video-box:hover a span {
	color: #a82d49;
}

/*  FEATURES */

.features-content {
	padding: 100px 0;
}

.fc-info div {
	padding: 25px 15px;
}

.fc-info h4 {
	font-weight: 700;
	font-size: 18px;
	letter-spacing: 0;
	margin: 0 0 10px;
}

.fc-info p {
	font-size: 14px;
	line-height: 23px;
}

.fc-info a {
	color: #a82d49;
	text-transform: uppercase;
	font-weight: 700;
	font-size: 12px;
	font-family: Roboto;
}

.fc-info a i {
	margin-left: 5px;
}

/*  PRICING TABLE */
.pricing {
	padding: 100px 0;
	background: #a82d49;
}

.popular {
	background: #222;
	height: 40px;
	line-height: 40px;
	position: absolute;
	left: 0px;
	right: 0px;
	width: 100%;
	top: -40px;
	color: rgb(255, 255, 255);
	text-align: center;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-radius: 3px 3px 0 0;
	font-family: Montserrat;
}

.pricing-item {
	margin: 0;
	padding: 40px 40px 40px;
	text-align: left;
	color: #000;
	background: #fff;
	position: relative;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
	border: none;
	border-radius: 3px;
}

.pricing-item__popular {
	border-radius: 0 0 3px 3px;
}

.pricing-title {
	font-size: 20px;
	margin: 0px 0px 10px;
	color: #ff4800;
	font-weight: 700;
	letter-spacing: 0.1px;
    line-height: 28px;
}

.pricing-price {
	font-size: 42px;
	font-weight: 700;
	padding: 10px 0 20px;
	color: #000;
}

.pricing-currency {
	font-size: 25px;
	vertical-align: super;
	color: #000;
}

.pricing-period {
	font-size: 20px;
	padding: 0 0 0 0.5em;
	color: #000;
}


.pricing-sentence {
	padding: 0 0 0.5em;
	margin: 0;
	color: #000;
}

.pricing-feature-list {
	font-size: 0.95em;
	margin: 0;
	padding: 0 0 2.5em;
	list-style: none;
	color: #666;
}

.pricing-feature {
	position: relative;
	display: block;
	padding: 0px 0px 5px 20px;
	line-height: 1.5;
	color: #666;
}

.pricing-feature::before {
	content: '';
	position: absolute;
	width: 6px;
	height: 2px;
	background: #000;
	opacity: 0.2;
	left: 0;
	top: 50%;
	margin: -2px 0 0 0;
}

.pricing-action {
	margin-top: auto;
	font-size: 1.55em;
	width: 60px;
	height: 60px;
	line-height: 60px;
	color: #fff;
	border-radius: 30px;
	background: #fff;
	border: none;
	-webkit-transition: background-color 0.3s, color 0.3s;
	transition: background-color 0.3s, color 0.3s;
	float: right;
}

.pricing-item .btn {
	background: #ff4800;
	border-color: #ff4800;
	width: 100%;
	margin: 0 auto;
	position: relative;
	bottom: 0px;
	left: 0px;
	right: 0px;
	color: #fff;
}

.pricing-item:nth-child(2) .pricing-action {
	background: #E25ABC;
}

.pricing-item:nth-child(3) .pricing-action {
	background: #7E5AE2;
}

.pricing-action:hover,
.pricing-action:focus {
	background: #1A1F28 !important;
}

.pricing .container {
	max-width: 1000px;
}


/*  CLIENTS */

.clients {
	padding: 50px 0;
}

.clients li {
	width: 20%;
	float: left;
	padding: 0 30px;
}

.clients img {
	opacity: 0.3;
	transition: .4s;
}

.clients img:hover {
	opacity: 1;
	transition: .4s;
}

/*  PAGE HEADER */

.page_head {
	background: url(../images/bg/1.jpg) no-repeat top center;
	background-size: cover;
	padding: 160px 0 80px;
}

.page_head h3 {
	color: #fff;
	font-size: 55px;
	font-weight: 700;
	margin: 0 0 10px;
}

.page_head p {
	color: #fff;
}


/*  404 STYLES */

.error-content {
	padding: 150px 0;
	border-bottom: 1px solid #eee;
	background-color: #c1c1c1;
}

.error-content h4 {
	color: #fff;
	font-size: 90px;
	font-weight: 700;
	letter-spacing: -7px;
	margin: 0 0 17px;
	text-shadow: 3px 4px 5px #aaa;
	text-shadow: 3px 4px 5px rgba(0,0,0,0.3);
}

.error-content p {
	font-size: 18px;
	margin: 0 0 40px;
}




/*  CONTACT */

#contact-info {
	padding: 0px 0;
	padding-bottom: 80px;
}

.c-info {
	padding: 25px;
	text-align: center;
	box-shadow: 0px 7px 16px 0px rgba(50, 50, 50, 0.2);
	border-radius: 15px;
}

.c-info i {
	color: #a82d49;
	font-size: 45px;
	margin: 0 0 15px;
}

.c-info h5 {
	text-transform: uppercase;
	font-size: 16px;
	margin: 15px 0 7px;
}

.c-info p {
}

#contact-form {
	background: #a82d49;
	padding: 100px 0;
}

#contactForm input {
	height: 50px;
	padding: 0 20px;
	font-size: 14px;
	letter-spacing: 0px;
	font-weight: 400;
	width: 100%;
	border: none;
	border-radius: 0px;
	background: transparent;
	border: 3px solid #e5e5e5;
	color: #000;
	font-weight: 700;
	margin: 0 0 20px;
}

#contactForm select {
	height: 50px;
	padding: 0 20px;
	font-size: 14px;
	letter-spacing: 0px;
	font-weight: 400;
	width: 100%;
	border: none;
	border-radius: 0px;
	background: transparent;
	border: 3px solid #e5e5e5;
	color: #000;
	font-weight: 700;
	margin: 0 0 20px;
}

#contactForm textarea {
	height: 100px;
	padding: 20px;
	font-size: 14px;
	letter-spacing: 0;
	font-weight: 400;
	width: 100%;
	margin: 0 0 20px;
	background: #fff;
	border: 3px solid #e5e5e5;
	border-radius: 0px;
	color: #fff;
}

#contact-form button {
	display: table;
	margin: 0 auto;
}

.error-info {
	padding: 180px 0;
}

.error-info h1 {
	font-size: 200px;
}

.error-info p {
	padding: 0px 25%;
}

.statusMessage, .successmessage, .errormessage {
	display: none;
	width: 100%;
	background: #fff;
	margin: 0px auto 15px;
	padding: 20px;
	border-radius: 0px;
}

.errormessage p, .statusMessage p, .successmessage p {
	margin: 0px !important;
	color: #000;
	font-size: 16px;
	letter-spacing: 0;
	margin: 0 !important;
}

.success-ico {
	background: url(../images/success.png);
	width: 25px;
	height: 25px;
	float: left;
	margin-right: 15px;
	position: relative;
	top: 0px;
	background-size: 25px;
}

.error-ico {
	background: url(../images/error.png);
	width: 25px;
	height: 25px;
	float: left;
	margin-right: 15px;
	position: relative;
	top: 0px;
	background-size: 25px;
}

#sendingMessage i {
	color: #00BCD4;
	font-size: 18px;
	margin-right: 10px;
}

#incompleteMessage i {
	color: yellow;
	font-size: 18px;
	margin-right: 10px;
}

/*  MAILCHIMP */

.ketchup-error {
	display: none !important;
}

#result {
	width: 100%;
	display: table;
	text-align: center;
	font-size: 12px;
	padding: 5px 15px;
	margin-top: 5px;
}

/* ELEMENTS */

.elements-content {
	padding: 10px 0;
}

.border-top {
	border-top: 1px solid #eee;
}

.progress-bar-success {
	background-color: #a82d49;
}

.progress {
	margin-top: 40px;
	position: relative;
	overflow: visible;
}

.progress h5 {
	position: absolute;
	top: -22px;
	font-size: 14px;
	font-weight: 400;
	letter-spacing: 0px;
	color: #000;
}

.progress-bar {
	border-radius: 4px;
}

.progress-stack .progress-bar {
	border-radius: 0px;
}

.progress-stack .progress-bar:first-child {
	border-radius: 4px 0 0 4px;
}

.progress-stack .progress-bar:last-child {
	border-radius: 0 4px 4px 0;
}

#accordion .panel-heading {
	padding: 0;
}

.panel-title > a {
	padding-left: 45px !important;
	position: relative;
}

#accordion .panel-title > a {
	display: block;
	padding: 18px 20px;
	outline: none;
	font-weight: 400;
	text-decoration: none;
	background: transparent;
	text-transform: uppercase;
	font-size: 14px;
	letter-spacing: 0;
}

.accordion-faq .panel-title > a {
	text-transform: none !important;
	color: #000;
	font-size: 15px !important;
}

#accordion .panel-title > a.accordion-toggle::before, #accordion a[data-toggle="collapse"]::before {
	content: "\f077";
	float: left;
	font-family: 'FontAwesome';
	margin-right: 1em;
	position: absolute;
	left: 15px;
	top: 50%;
	margin-top: -8px;
	font-size: 15px;
}

#accordion .panel-title > a.accordion-toggle.collapsed::before, #accordion a.collapsed[data-toggle="collapse"]::before {
	content: "\f078";
}

#accordion .panel-default > .panel-heading {
	background-color: transparent;
	border-color: #ddd;
}

#accordion .panel {
	margin-bottom: 15px;
	border-radius: 0 !important;
}

#accordion .panel-body {
	padding: 20px 25px;
	line-height: 24px;
	font-size: 15px;
}

.accordion-dark .panel {
	background: transparent;
	border: 1px solid #444;
}

.accordion-dark .panel-default > .panel-heading {
	border-color: #444 !important;
}

.accordion-dark .panel-default > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: #444;
}

.accordion-dark .panel-title > a {
	color: #fff;
}
.faq{
	margin-bottom: 30px;
}	
.faq h4{
	color:#000);
	font-size: 18px;
	font-weight:bold;
	letter-spacing:0.2px;
}
.faq h4 span{
	color:#f98169;
	font-size: 28px;
	font-family: ubuntu;
	font-weight: 300;
	vertical-align: middle;
}

.bg-primary {
	background: #a82d49;
}

.bg-primary-image {
	background:url(../images/5.jpg);
}

.bg-dark {
	background: #222;
}

.bg-dark-image {
	background:url(../images/6.jpg);
}

.nav-tabs {
	border-bottom: 2px solid #DDD;
}

.nav-tabs > li.active > a, .nav-tabs > li.active > a:focus, .nav-tabs > li.active > a:hover {
	border-width: 0;
}

.nav-tabs > li > a {
	border: none;
	color: #666;
	font-family: Roboto;
	color: #000;
	text-transform: uppercase;
	font-size: 13px;
}

.nav-tabs > li.active > a, .nav-tabs > li > a:hover {
	border: none;
	color: #a82d49 !important;
	background: transparent;
}

.nav-tabs > li > a::after {
	content: "";
	background: #a82d49;
	height: 2px;
	position: absolute;
	width: 100%;
	left: 0px;
	bottom: -1px;
	transition: all 250ms ease 0s;
	transform: scale(0);
}

.nav-tabs > li.active > a::after, .nav-tabs > li:hover > a::after {
	transform: scale(1);
}

.tab-nav > li > a::after {
	background: #a82d49;
	color: #fff;
}

.tab-pane {
	padding: 15px 0;
}

.tab-content {
	padding: 25px 15px 0;
	text-align: center;
}

.lead a {
	color: #a82d49;
}

.blog-excerpt p {
	font-size: 15px;
	letter-spacing: 0.3px;
	line-height: 25px;
	margin: 0 0 15px;
}

blockquote {
	padding: 11px 20px;
	margin: 20px 3% 30px;
	border-left: 5px solid #a82d49;
	line-height: 31px;
	font-style: italic;
	color: #43121d;
	font-size: 16px;
	font-family: Roboto;
}

blockquote cite {
	font-family: Roboto;
	display: block;
	font-style: normal;
	text-transform: uppercase;
	font-size: 12px;
	font-weight: bold;
	padding-top: 10px;
}

blockquote.inverse {
	border-left: none;
	border-right: 5px solid #a82d49;
	text-align: right;
}

blockquote.pull-left {
	width: 35%;
	margin-right: 20px;
	margin-bottom: 10px;
	margin-left: 0px;
}

blockquote.pull-right {
	width: 35%;
	margin-right: 0px;
	margin-bottom: 10px;
	margin-left: 20px;
}

.clients2 {
	list-style: none;
	overflow: hidden;
	display: block;
	width: 100%;
}

.clients2 li {
	width: 20%;
	float: left;
	position: relative;
	padding: 40px 30px;
}

.clients2 li:before {
	content: "";
	height: 100%;
	top: 0;
	left: -1px;
	position: absolute;
	border-left: 1px solid #e5e5e5;
}

.clients2 li:after {
	content: "";
	width: 100%;
	height: 0;
	top: auto;
	left: 0;
	bottom: -1px;
	position: absolute;
	border-bottom: 1px solid #e5e5e5;
}

.clients2 img {
	opacity: 0.3;
	transition: .4s;
	max-width: 140px;
}

.clients2 img:hover {
	opacity: 1;
	transition: .4s;
}

.process-step .btn:focus {
	outline: none;
}

.process {
	display: table;
	width: 100%;
}

.process-row {
	display: table-row;
}

.process-step button[disabled] {
	opacity: 1 !important;
	filter: alpha(opacity=100) !important;
}

.process-row:before {
	top: 40px;
	bottom: 0;
	position: absolute;
	content: " ";
	width: 100%;
	height: 1px;
	background-color: #e5e5e5;
	z-order: 0;
}

.process-step {
	display: inline;
	text-align: center;
	position: relative;
	float: left;
	width: 20%;
}

.process-step p {
	margin-top: 4px;
}

.btn-circle {
	width: 80px;
	height: 80px;
	text-align: center;
	font-size: 12px;
	border-radius: 50%;
	padding: 0;
	line-height: 91px;
	font-style: normal;
	border: 1px solid #e5e5e5 !important;
}

.btn-circle:hover,
.btn-info {
	background: #a82d49 !important;
	border-color: #a82d49 !important;
}

.process-step .btn .fa {
	margin-left: 0;
	position: relative;
	top: 0px;
	font-size: 25px;
}

.process-step small {
	display: block;
	font-family: Roboto;
	color: #000;
	text-transform: uppercase;
	padding-top: 18px;
}

.process-step2 .btn:focus {
	outline: none;
}

.process2 {
	display: table;
	width: 100%;
	position: relative;
}

.process-row2 {
	display: table-row;
}

.process-step2 button[disabled] {
	opacity: 1 !important;
	filter: alpha(opacity=100) !important;
}

.process-step2 {
	display: inline;
	text-align: center;
	position: relative;
	float: left;
	width: 25%;
}

.process-step2 p {
	margin-top: 4px;
}

.process-step2 .btn-circle {
	width: 80px;
	height: 80px;
	text-align: center;
	font-size: 12px;
	border-radius: 50%;
	padding: 0;
	line-height: 91px;
	font-style: normal;
	border: 1px solid #555 !important;
	background: #555;
	color: #fff;
}

.process-step2 .btn-circle:hover,
.process-step2 .btn-info {
	background: #a82d49 !important;
	border-color: #a82d49 !important;
}

.process-step2 .btn .fa {
	margin-left: 0;
	position: relative;
	top: 0px;
	font-size: 25px;
}

.process-step2 small {
	display: block;
	font-family: Roboto;
	color: #999;
	text-transform: uppercase;
	padding-top: 18px;
}

.process-row2:before {
	top: 40px;
	bottom: 0;
	position: absolute;
	content: " ";
	width: 76%;
	height: 1px;
	border-bottom: 1px dashed #555;
	z-order: 0;
	margin: 0 auto;
	display: table;
	left: 0;
	right: 0;
}

.process2 + .tab-content h3 {
	color: #fff;
}

.process2 + .tab-content p {
	color: #fff;
	opacity: 0.4;
}

/*  FOOTER */

footer {
	background: #43121d;
	padding: 80px 0 0;
}

footer .list-inline li {
	width: 20%;
	padding: 0;
	display: inline;
	float: left;
}

.contact-info {
	padding: 10px 0;
	text-align: center;
	color: #000;
	font-weight: 400;
	font-size: 13px;
	border-right: 1px solid #fff;
	font-family: Roboto;
	letter-spacing: 0.5px;
}

.contact-info i {
	color: #a82d49;
	display: block;
	font-size: 25px;
	margin-bottom: -5px;
}

.footer-social {
	padding-left: 25px;
	margin-top: 10px;
}

.footer-social a {
	font-size: 19px;
	margin-left: 12px;
	margin-top: 30px;
}

.footer-logo {
	line-height: 110px;
	padding: 0px;
	font-size: 24px;
	font-weight: 700;
	text-transform: uppercase;
	color: #000;
	margin: 0;
	position: relative;
	font-family: Roboto;
	letter-spacing: 4px;
}

.footer-storeicon img {
	max-width:150px;
}

.footer-copy {
	background: #a82d49;
	padding-bottom: 50px;
	text-align: center;
	color: #fff;
	font-size: 13px;
	letter-spacing: 0.2px;
}

.slick-dots {
	margin: 15px auto;
	position: relative;
	left: 0;
	display: table !important;
}

.slick-dots li {
	float: left;
	margin-right: 7px;
}

.slick-dots li button {
	border: none;
	width: 8px;
	height: 8px;
	font-size: 0px;
	padding: 0;
	border-radius: 50%;
	opacity: 0.2;
}

.slick-dots li.slick-active button {
	opacity: 1;
}




/*  Explore Foods */

.card-product .img-wrap {
    border-radius: 3px 3px 0 0;
    overflow: hidden;
    position: relative;
    height: 260px;
    text-align: center;
}
.card-product .img-wrap img {
    max-height: 100%;
    max-width: 100%;
    object-fit: cover;
	border-radius: 10px;
}
.card-product .info-wrap {
    overflow: hidden;
    padding: 15px;
}
.card-product .bottom-wrap {
    padding: 15px;
    border-top: 1px solid #eee;
}
.card-product .price-old {
    color: #999;
}

.price-new{
	font-weight: bold;
	color: #a82d49;
}

.price-wrap h3{
 margin-top: 15px;
margin-bottom: 5px;
}


.video-action {
    background-image: url(../images/parallax/1.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    color: #fff;
    height: 450px;
    padding-top: 50px;
	border-radius: 10px;
}

.video-action h1{
	color: #a82d49;
	padding-top:40px;
}

.video-action p{
	color: #555;
	padding-top:20px;
}



.parallax-content {
	background: url(../images/parallax/1.jpg) no-repeat center fixed;
	background-size: cover;
	padding: 200px 0;
	position: relative;
}

.parallax-content2 {
	padding: 70px 0;
	position: relative;
}

.parallax-content2 .overlay {
	background: #000;
	opacity: 0.2;
}

.parallax-content2 .overlay {
	background: #000;
	opacity: 0.4;
}

.parallax-content .overlay {
	background: #000;
	opacity: 0.2;
}

.parallax-content .container {
	position: relative;
	z-index: 55;
}

.parallax2 {
	background:url(../images/parallax/2.jpg) no-repeat center fixed;
	background-size: cover;
}



.parallax-content h4 {
	color: #fff;
	font-size: 28px;
	font-weight: 700;
	text-align:left;
	letter-spacing: 0.2px;
}

.parallax-content2 h4 {
	color: #fff;
	font-size: 28px;
	font-weight: 700;
	text-align:left;
	letter-spacing: 0.2px;
}

.parallax-content h5 {
	color: #fff;
	font-size: 19px;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 2px;
}

.parallax-content p {
	color: #fff;
	font-size: 16px;
	text-align:left;
	padding: 10px 0 0;
}

.testimonials-color {
	background-color: #a82d49;
}

.testimonials-color h3{
	color: #fff;
}

.testimonials-color p {
    font-size: 21px;
    line-height: 33px;
    font-weight: 300;
    color: #111;
    padding: 0 21% 20px;
    letter-spacing: 0;
}

.testimonials-color .author {
	font-weight: bold;
	font-size: 16px;
	text-transform:capitalize;
	letter-spacing: 0.2px;
	color: #a82d49;
}
.testimonials-color .author-job {
	font-size: 13px;
	text-transform:capitalize;
	letter-spacing: 0.2px;
	color: #555;
}

.testimonials-color i {
	color: #333;
	font-size: 75px;
	margin: 0px auto 30px;
	display: table;
}

.quote2 .slick-dots {
	margin: 15px auto;
	position: relative;
	left: 0;
	display: table !important;
}

.quote2 .slick-dots li {
	float: left;
	margin-right: 7px;
}

.quote2 .slick-dots li button {
	border: none;
	width: 15px;
	height: 15px;
	font-size: 0px;
	padding: 0;
	border-radius: 50%;
	opacity: 0.2;
	background-color: #ffffff;
}

.quote2 .slick-dots li.slick-active button {
	opacity: 1;
}

.text-white {
	color: #fff !important;
}

#stats1 {
	padding: 120px 0;
}

.stats1-info {
	text-align: center;
}

.stats1-info p {
	color: #000;
	font-size: 40px;
	margin: 0 0 20px;
	font-family: Roboto;
}

.stats1-info h2 {
	color: #000;
	font-size: 12px;
	font-family: Roboto;
	text-transform: uppercase;
	letter-spacing: 3px;
	font-weight: 700;
}

.stats1-info i {
	color: #a82d49;
	font-size: 40px;
	margin: 0 auto 25px;
	display: table;
}

#stats2 {
	padding: 40px 0;
}

.stats2-info {
	text-align: center;
}

.stats2-info p {
	color: #fff;
	font-size: 40px;
	margin: 0 0 20px;
	font-family: Roboto;
}

.stats2-info h2 {
	color: #fff;
	font-size: 12px;
	font-family: Roboto;
	text-transform: uppercase;
	letter-spacing: 3px;
	font-weight: 700;
}

.stats2-info i {
	color: #fff;
	font-size: 40px;
	margin: 0 auto 25px;
	display: table;
}

.elements-content p {
	font-size: 16px;
}

.testimonials-color.parallax-content {
	padding: 0;
}

.testimonials-color.parallax-content .col-md-6 {
	padding: 120px 30px;
}

.no-margin {
	margin: 0 !important;
}

.timeline {
	list-style: none;
	padding: 20px 0 50px;
	position: relative;
}

.timeline:before {
	top: 0;
	bottom: 0;
	position: absolute;
	content: " ";
	width: 3px;
	background-color: #eeeeee;
	left: 50%;
	margin-left: -1.5px;
}

.timeline:after {
	content: "";
	width: 30px;
	height: 30px;
	background: #fff;
	position: absolute;
	bottom: 0;
	left: -1px;
	right: 0;
	display: table;
	margin: 0 auto;
	border-radius: 50%;
	border: 4px solid #eeeeee;
}

.timeline > li {
	margin-bottom: 20px;
	position: relative;
}

.timeline > li:before,
        .timeline > li:after {
	content: " ";
	display: table;
}

.timeline > li:after {
	clear: both;
}

.timeline > li:before,
        .timeline > li:after {
	content: " ";
	display: table;
}

.timeline > li:after {
	clear: both;
}

.timeline > li > .timeline-panel {
	width: 45%;
	float: left;
	border: 1px solid #e5e5e5;
	border-radius: 2px;
	padding: 30px 30px 27px;
	position: relative;
}

.timeline > li > .timeline-panel:before {
	position: absolute;
	top: 26px;
	right: -15px;
	display: inline-block;
	border-top: 15px solid transparent;
	border-left: 15px solid #ccc;
	border-right: 0 solid #ccc;
	border-bottom: 15px solid transparent;
	content: " ";
}

.timeline > li > .timeline-panel:after {
	position: absolute;
	top: 27px;
	right: -14px;
	display: inline-block;
	border-top: 14px solid transparent;
	border-left: 14px solid #fff;
	border-right: 0 solid #fff;
	border-bottom: 14px solid transparent;
	content: " ";
}

.timeline > li > .timeline-badge {
	color: #fff;
	width: 70px;
	height: 70px;
	line-height: 78px;
	font-size: 25px;
	text-align: center;
	position: absolute;
	top: 4px;
	left: 50%;
	margin-left: -35px;
	background-color: #999999;
	z-index: 100;
	border-top-right-radius: 50%;
	border-top-left-radius: 50%;
	border-bottom-right-radius: 50%;
	border-bottom-left-radius: 50%;
}

.timeline > li.timeline-inverted > .timeline-panel {
	float: right;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
	border-left-width: 0;
	border-right-width: 15px;
	left: -15px;
	right: auto;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
	border-left-width: 0;
	border-right-width: 14px;
	left: -14px;
	right: auto;
}

.timeline-badge.primary {
	background-color: #2e6da4 !important;
}

.timeline-badge.success {
	background-color: #3f903f !important;
}

.timeline-badge.warning {
	background-color: #f0ad4e !important;
}

.timeline-badge.danger {
	background-color: #d9534f !important;
}

.timeline-badge.info {
	background-color: #5bc0de !important;
}

.timeline-title {
	margin-top: 0;
	color: inherit;
	color: #000;
	margin: 0 0 12px;
	font-size: 20px;
	text-transform: uppercase;
}

.timeline-body > p,
.timeline-body > ul {
	margin-bottom: 0;
}

.timeline-body > p + p {
	margin-top: 5px;
}

@media (max-width: 767px) {
	ul.timeline:before {
		left: 40px;
	}

	ul.timeline > li > .timeline-panel {
		width: calc(100% - 105px);
		width: -moz-calc(100% - 105px);
		width: -webkit-calc(100% - 105px);
	}

	ul.timeline > li > .timeline-badge {
		left: 7px;
		margin-left: 0;
		top: 16px;
	}

	ul.timeline > li > .timeline-panel {
		float: right;
	}

	ul.timeline > li > .timeline-panel:before {
		border-left-width: 0;
		border-right-width: 15px;
		left: -15px;
		right: auto;
	}

	ul.timeline > li > .timeline-panel:after {
		border-left-width: 0;
		border-right-width: 14px;
		left: -14px;
		right: auto;
	}

	.timeline::after {
		left: 24px;
		right: auto;
	}
}

.event-list li {
	margin-bottom: 15px;
	overflow: hidden;
	position: relative;
	border: 1px solid #F2EDED;
}

.event-list li time, .event-list li img {
	width: 110px;
	height: 110px;
	padding: 0px;
	margin: 0px;
	float: left;
	display: inline;
}

.event-list li time {
	color: #fff;
	background: #E91E63;
	padding: 5px;
	text-align: center;
	text-transform: uppercase;
}

.event-list li:nth-child(2) time {
	background: #673AB7;
}

.event-list li:nth-child(3) time {
	background: #a82d49;
}

.event-list li time .day {
	display: block;
	font-size: 41px;
	font-weight: 300;
	margin: 7px 0 -5px;
	font-family: Roboto;
}

.event-list li time .month {
	font-size: 16px;
	font-weight: 300;
}

.event-list li time .year {
	font-size: 16px;
	font-weight: 700;
}

.event-list li .info {
	padding: 15px 30px;
	height: 110px;
	border-left: 0px;
	border-radius: 0 3px 3px 0;
	float: left;
}

.event-list li .info h2 {
	font-size: 16px;
	color: #000;
	display: table;
	width: 100%;
	text-transform: uppercase;
	letter-spacing: 0.2px;
	font-weight: 700;
}

.event-list li .info h2 span {
	display: block;
	font-family: Roboto;
	font-size: 12px;
	color: #222;
	letter-spacing: 0px;
	margin-top: 5px;
	text-transform: uppercase;
	font-weight: 700;
}

.event-list li .info h2 span + span {
	display: block;
	font-family: Roboto;
	font-size: 11px;
	color: #555;
	letter-spacing: 0px;
	margin-top: 15px;
	text-transform: none;
	font-weight: 400;
}

.ticket {
	position: absolute;
	top: 50%;
	right: 25px;
	font-size: 27px;
	margin-top: -18px;
	opacity: 0.3;
}

.ticket:hover {
	opacity: 1;
}

.cta-wrap {
	padding: 80px 0 65px;
}

.cta-wrap h2 {
	margin: 0 0 7px;
	font-size: 26px;
	text-transform: uppercase;
	font-weight: 700;
	letter-spacing: 0;
}

.cta-wrap.bg-dark h2 ,
.cta-wrap.bg-primary h2 {
	color: #fff;
}

.cta-wrap.bg-dark p ,
.cta-wrap.bg-primary p {
	color: #fff;
	opacity: 0.5;
}

.cta-wrap.bg-dark .cta-btn ,
.cta-wrap.bg-primary .cta-btn {
	color: #fff;
	border: 2px solid #fff;
}

.cta-btn:hover {
	background: #a82d49;
	border: 2px solid #a82d49;
	color: #fff;
}

.cta-wrap.bg-dark .cta-btn:hover {
	color: #fff;
	background: #a82d49;
	border: 2px solid #a82d49;
}

.cta-wrap.bg-primary .cta-btn:hover {
	color: #fff;
	background: #222;
	border: 2px solid #222;
}

.gold, .silver, .plat {
	text-align: center;
	padding: 30px 50px 50px;
	border-radius: 7px;
	box-shadow: 0px 7px 16px 0px rgba(50, 50, 50, 0.2);
}

.gold {
	position: relative;
	top: 0px;
	background: #43121d;
	text-align: center;
	border: 1px solid #43121d;
}

.pricing2 .gold > .price {
	background: #fff;
	color: #222;
}

.pricing2 .gold > h1, .pricing2 .gold > h2, .pricing2 .gold > p, .pricing2 .gold > span {
	color: #fff;
}

.pricing2 .price {
	height: 120px;
	width: 120px;
	text-align: center;
	background-color: #a82d49;
	border-radius: 50%;
	line-height: 120px;
	color: #fff;
	font-size: 40px;
	font-family: Roboto;
	font-weight: 400;
	margin: 20px auto;
}

.pricing2 h1 {
	margin: 20px 0 10px 0;
	font-size: 28px;
	color: #000;
	font-weight: 700;
}

.pricing2 h2 {
	font-size: 11px;
	color: #555;
	font-weight: 300;
	letter-spacing: 0.2px;
}

.pricing2 p {
	color: #444;
	margin: 10px 0;
	font-weight: 100;
	font-size: 14px;
}

.pricing2 span {
	margin-bottom: 20px;
	padding-bottom: 10px;
	display: inline-block;
	width: 125px;
	font-size: 1em;
	font-weight: 700;
	letter-spacing: 0.2px;
	color: rgba(0, 0, 0, 0.5);
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.pricing2 .btn {
	background: #a82d49;
	border-color: #a82d49;
	margin: 15px auto 0;
	position: relative;
	bottom: 0px;
	left: 0px;
	right: 0px;
	color: #fff;
	padding: 12px 35px;
}

.pricing2 .btn:hover {
	background: #43121d;
	border-color: #43121d;
	color: #fff;
}

.pricing2 .gold .btn:hover {
	background: #fff;
	border-color: #fff;
	color: #43121d;
}

#contact-form2 {
	padding: 0;
}

#contact-form2 .google-map,
#contact-form2 #map {
	height: 640px;
}

#contact-form2 .col-md-6 {
	padding: 100px;
}

#contact-form2 .col-md-6 .col-md-6 {
	padding: 0px 15px;
}

#contact-form2 h3 {
	font-size: 40px;
	font-weight: 700;
	margin: 0px 0px 10px;
}

#contact-form2 #contactForm input {
	border: 2px solid #e5e5e5;
	box-shadow: none !important;
	color: #000 !important;
	border-bottom-left-radius: 20px;
	border-bottom-right-radius: 20px;
}

#contact-form2 #contactForm textarea {
	padding: 15px 20px;
	border: 2px solid #e5e5e5;
	box-shadow: none !important;
	color: #000 !important;
	border-bottom-left-radius: 20px;
	border-bottom-right-radius: 20px;
}

input:focus, textarea:focus {
	box-shadow: none;
	outline: 0;
}

.footer2 {
	background: #a82d49;
}

.footer2 .google-map {
	border: 1px solid #555;
	padding: 5px;
	height: 290px;
}

.footer2 #map {
	height: 280px;
}

.footer2 #contactForm input {
	height: 43px;
	padding: 0 20px;
	font-size: 14px;
	letter-spacing: 0;
	font-weight: 400;
	width: 100%;
	margin: 0 0 10px;
	background: #222;
	border: none;
	border-radius: 5px;
	color: #fff;
	border-top: 1px solid #414141;
}

.footer2 #contactForm textarea {
	height: 80px;
	padding: 12px 20px;
	font-size: 14px;
	letter-spacing: 0;
	font-weight: 400;
	width: 100%;
	margin: 0 0 20px;
	background: #222;
	border: none;
	border-radius: 5px;
	color: #fff;
	border-top: 1px solid #414141;
}

.footer2 h5 {
	color: #ffffff;
	font-size: 16px;
	font-weight: bolder;
	text-transform: uppercase;
	margin: 0px 0px 15px;
	font-size: 16px;
	letter-spacing: 0px;
}

.footer2 .footer-logo {
	line-height: 20px;
	color: #fff;
	margin-bottom: 20px;
	display: block;
}

.footer2 p {
	color: #fff;
	font-size: 14px;
}

.footer2 .footer-social a {
	font-size: 25px;
	margin-right: 10px;
	margin-left: 0px;
	margin-top: 30px;
	color: #fff;
}

.footer2 .footer-social {
	padding-left: 0;
	margin-top: -30px;
}

.subscribe-elt .intro-newsletter {
	width: 100%;
	position: relative;
}

.subscribe-elt .intro-newsletter input {
	height: 66px;
	padding: 0 150px 0 25px;
	font-size: 16px;
	letter-spacing: 0.04em;
	font-weight: 400;
	width: 100%;
	border: none;
	border-radius: 0px;
	background: transparent;
	border: 2px solid #555;
	color: #fff;
	border-radius: 3px;
}

.subscribe-elt .intro-newsletter button {
	border-radius: 3px;
	position: absolute;
	top: 11px;
	right: 10px;
	width: 100px;
	font-size: 11px;
	height: 45px;
}

.subscribe-elt h5 {
	color: #ddd;
	font-size: 18px;
	line-height: 26px;
	width: 75%;
	letter-spacing: 0px;
	font-weight: 600;
	margin-top: 5px;
}

.subscribe-elt2 h5 {
	font-family: Roboto;
	font-size: 40px;
	color: #000;
	font-weight: 600;
	letter-spacing: 0.4px;
	text-align: center;
	width: 100%;
}

.subscribe-elt2 p {
	color: #555;
	text-align: center;
	width: 100%;
	letter-spacing: 0.2px;
	padding: 25px 0 15px;
}

.subscribe-elt2 .intro-newsletter input {
	height: 50px;
	padding: 0 20px;
	font-size: 14px;
	letter-spacing: 0px;
	font-weight: 400;
	width: 100%;
	border: none;
	border-radius: 0px;
	background: transparent;
	border: 3px solid #e5e5e5;
	color: #000;
	font-weight: 700;
}

.subscribe-elt2 .intro-newsletter button {
	position: relative;
	top: 0px;
	right: 0px;
	width: 100%;
	font-size: 14px;
	height: 50px;
}

.subscribe-elt2 {
	padding: 120px 0 50px 0;
}

.subscribe-elt3 h5 {
	font-family: Roboto;
	font-size: 40px;
	color: #fff;
	font-weight: 300;
	letter-spacing: 0.4px;
	text-align: center;
	width: 100%;
}

.subscribe-elt3 p {
	color: #fff;
	text-align: center;
	width: 100%;
	letter-spacing: 0.2px;
	opacity: 0.4;
	padding: 10px 0 15px;
}

.services-s5 .service-content i {
	display: table;
	font-size: 40px;
	margin: 0 auto 20px;
	padding: 0px;
	cursor: pointer;
	background: transparent;
	text-decoration: none;
	color: #d1d1d1;
	width: 90px;
	height: 90px;
	line-height: 88px;
	border-radius: 50%;
	vertical-align: middle;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	box-shadow: 0 0 1px rgba(0, 0, 0, 0);
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-moz-osx-font-smoothing: grayscale;
	position: relative;
	text-align: center;
}

.services-s5 .service-content i:after {
	content: '';
	position: absolute;
	border: #d1d1d1 solid 2px;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	-webkit-transition-duration: 0.3s;
	transition-duration: 0.3s;
	-webkit-transition-property: top, right, bottom, left;
	transition-property: top, right, bottom, left;
	border-radius: 50%;
}

.services-s5 .service-content:hover i:after {
	top: -6px;
	right: -6px;
	bottom: -6px;
	left: -6px;
	border: #a82d49 solid 3px;
}

.services-s5 .service-content:hover i {
	color: #fff;
	background: #a82d49;
}

.services-s5 .service-content h4 {
	font-size: 19px;
	margin: 5px 0 14px;
	text-align: center;
	font-weight: 700;
}

.services-s5 .service-content p {
	font-size: 13px;
	text-align: center;
}

.icon-features {
	background: #fff;
}

.feature-left {
	margin: 80px 0 0;
	text-align: right;
	padding-right: 30px;
}

.feature-left li {
	margin-bottom: 30px;
}

.feature-left li i {
	font-size: 45px;
	color: #a82d49;
}

.fl-content h4 {
	margin: 30px 0 10px;
	font-size: 20px;
	font-weight: 700;
	color: #000;
}

.fl-content p {
	font-size: 14px;
	padding-left: 40px;
	color: #000;
	opacity: 0.5;
	margin-bottom: 40px;
}

.feature-right {
	margin: 80px 0 0;
	text-align: left;
	padding-left: 30px;
}

.feature-right li {
	margin-bottom: 30px;
}

.feature-right li i {
	font-size: 45px;
	color: #a82d49;
}

.fr-content h4 {
	margin: 20px 0 10px;
	font-size: 20px;
	font-weight: 700;
	color: #000;
}

.fr-content p {
	font-size: 14px;
	padding-right: 40px;
	color: #000;
	opacity: 0.5;
	margin-bottom: 40px;
}

.quote2 div img {
	max-width: 80px;
	border-radius: 50%;
	background: #fff;
	padding: 5px;
	margin: -20px auto 10px;
}

.quote3 p {
	padding: 1px 10px 15px;
	font-size: 15px;
	font-style: italic;
	color: #555;
}

.quote3 div {
	margin: 0 15px;
}

.quote3 div img {
	max-width: 70px;
	border-radius: 50%;
	background: #fff;
	border: 1px solid #ddd;
	padding: 5px;
	margin: -20px auto 10px;
}

.quote3 .slick-dots {
	margin: 50px auto;
	position: relative;
	left: 0;
	display: table !important;
}

.quote3 .slick-dots li {
	float: left;
	margin-right: 7px;
}

.quote3 .slick-dots li button {
	border: none;
	width: 8px;
	height: 8px;
	font-size: 0px;
	padding: 0;
	border-radius: 50%;
	opacity: 0.2;
	border: 1px solid #000;
}

.quote3 .slick-dots li.slick-active button {
	opacity: 1;
}

.multi-content {
	background: #a82d49;
	padding: 100px 0;
}

.multi-content h3 {
	color: #fff;
	text-transform: uppercase;
	font-size: 16px;
	font-weight: 700;
	letter-spacing: 0px;
}

.multi-content h2 {
	color: #fff;
	font-size: 34px;
	letter-spacing: 0px;
	padding: 10px 0 15px;
}

.multi-content p {
	color: #fff;
	opacity: 0.7;
}

.multi-content img {
	position: relative;
	top: 100px;
	margin-top: -100px;
	margin-bottom: -100px;
	max-width: 100%;
}

.multi-features {
	background: #43121d;
	padding: 140px 0 100px;
}

.multi-features h4 {
	color: #fff;
	text-transform: uppercase;
	font-size: 14px;
	margin: 0px 0px 18px;
	letter-spacing: 0;
}

.multi-features h4 i {
	font-size: 24px;
	margin-right: 15px;
	position: relative;
	top: 4px;
}

.multi-features p {
	color: #888;
	font-size: 14px;
}

.cf2-wrap {
	max-width:600px;
	padding:25px;
}
.modal-header{
	border:none;
}

.parallax5  .col-md-6.bg-dark {
	max-width:800px;
}


#contact-form2 {
	max-width: 1200px;
	margin: 0 auto;
}

/* 25. RESPONSIVE STYLES */

@media only screen and (min-width: 768px)  and (max-width: 1230px) {

	footer .list-inline li {
		width: 33.3333%;
	}

	footer .list-inline li:nth-child(4) .contact-info {
		border: none;
	}

	footer .list-inline li:first-child ,
	footer .list-inline li:last-child {
		width: 100%;
		text-align: center;
	}

	footer {
		background: #dbdbdb;
		padding: 25px 0 60px;
	}
}

@media only screen and (min-width: 768px)  and (max-width: 1100px) {

	.clients2 img {
		max-width: 100%;
	}

	.about-inline .ai-slide {
		width: 750px;
		position: relative;
	}

	.about-inline .ai-slide .center-block {
		width: 750px;
	}

	.ai-slide-img {
		position: absolute;
		top: 0;
		left: 0;
		overflow: hidden;
	}

	.ai-slide-img {
		position: absolute;
		width: 493px;
		overflow: hidden;
		height: 306px;
		margin: 0 auto;
		display: block;
		left: -7px;
		right: 0;
		top: 26px;
	}
}

@media only screen and (min-width: 768px)  and (max-width: 992px) {

	.container {
		width: 100%;
		max-width: 750px;
	}

	.mega-menu {
		left: 0px !important;
	}

	.navbar-lg .navbar-nav > li > a {
		padding: 40px 5px 35px;
		font-size: 10px;
	}
	
	.icon-box-square .text-center{ margin-bottom: 50px; }

	.testimonials p {
		padding: 0 30px 07px;
	}

	.pricing-price {
		padding: 35px 0 30px;
		line-height: 20px;
	}

	.pricing-item {
		margin: 0;
		padding: 30px 30px 30px;
	}

	.team .team-box {
		margin-bottom: 30px;
		padding: 0 15px;
	}

	.info-content .btn.btn-lg {
		font-size: 11px;
		margin-bottom: 50px;
	}

	.info-content h3 {
		font-size: 36px;
		margin: 0px 0 20px;
	}

	.info-content p {
		margin: 0 0 10px;
		font-size: 13px;
		letter-spacing: 0.1px;
	}

	.center-content-ipad {
		display: flex;
		align-items: center;
		flex-direction: row;
	}

	.error-content {
		padding: 100px 0;
	}

	.error-content h4 {
		font-size: 75px;
		letter-spacing: -5px;
	}

	.sidebar {
		padding-top: 50px;
	}

	#contact-info .col-sm-6 {
		margin-bottom: 20px;
	}

	.team {
		padding: 100px 0 70px;
	}

	.portfolio-filter li {
		margin: 0 10px;
	}

	.portfolio-4col .portfolio-item {
		width: 50%;
	}

	.intro-form {
		padding: 30px 30px 40px;
	}

	.intro1 h2 {
		font-size: 42px;
	}

	.intro16 h3 {
		margin: 40px 0px 30px;
	}

	.hl-container {
		width: 100%;
		max-width: 550px;
		position: relative;
		float: none !important;
		margin: 0 auto;
	}

	.intro13 h3 {
		margin: 40px 0px 30px;
	}

	.intro10 h3 {
		margin: 40px 0px 25px;
	}

	.intro8 img {
		margin-top: 40px;
	}

	.intro3 h3 {
		margin: 0px 0px 25px;
		font-size: 38px;
	}
}

@media only screen and (min-width: 320px)  and (max-width: 370px) {

	.pricing-price{ margin-left: -27px;}
}
@media only screen and (min-width: 370px)  and (max-width: 370px) {

	.pricing-price{ margin-left: 10px;}
}

@media only screen and (min-width: 280px)  and (max-width: 767px) {

	.footer2 .footer-logo {
		text-align: left;
	}
	
	.icon-box-square .text-center{ margin-bottom: 50px; }
	
	.pricing-price{ margin-left: 20px ;}

	.footer2 .footer-social {
		padding-left: 0;
		padding-top: 30px;
	}

	.footer2 .col-md-4 {
		margin-bottom: 50px;
	}

	.services-s5 .service-content p {
		margin-bottom: 40px;
	}

	.feature-left {
		text-align: center;
		padding-right: 0;
		width: 100%;
	}

	.feature-right {
		text-align: center;
		padding-left: 0;
		width: 100%;
	}

	.fl-content p ,
.fr-content p {
		padding: 0;
	}

	.testimonials .quote3 p {
		font-size: 15px;
		padding: 30px 30px 70px;
	}

	.stats1-info h2 ,
.stats2-info h2 ,
.stats3-info h2 {
		margin-bottom: 40px;
	}

	#stats1,
#stats2 {
		padding: 120px 0 80px;
	}

	.process-step small {
		display: none;
	}

	.btn-circle {
		width: 50px;
		height: 50px;
		text-align: center;
		font-size: 7px !important;
		border-radius: 50%;
		padding: 0;
		line-height: 58px;
		font-style: normal;
	}

	.process-step .btn .fa {
		font-size: 20px;
	}

	.process-row:before {
		top: 24px;
	}

	.process-step2 small {
		display: none;
	}

	.process-step2 .btn-circle {
		width: 60px;
		height: 60px;
		text-align: center;
		font-size: 7px !important;
		border-radius: 50%;
		padding: 0;
		line-height: 68px;
		font-style: normal;
	}

	.process-row2:before {
		top: 32px;
	}

	.event-list li img {
		display: none;
	}

	blockquote.pull-left {
		width: 100%;
		max-width: 300px;
	}

	blockquote.pull-right {
		width: 100%;
		max-width: 300px;
	}

	.clients li {
		width: 50%;
		float: left;
		padding: 0 30px;
	}

	.clients2 img {
		max-width: 100%;
	}

	.clients2 li {
		width: 33.333%;
	}

	.about-inline .ai-slide {
		width: 300px;
		position: relative;
	}

	.about-inline .ai-slide .center-block {
		width: 300px;
	}

	.ai-slide-img {
		position: absolute;
		width: 196px;
		overflow: hidden;
		height: 123px;
		margin: 0 auto;
		display: block;
		left: -1px;
		right: 0;
		top: 11px;
	}

	.about-inline h3 br {
		display: inline-block;
	}

	.intro-newsletter {
		width: 75%;
	}

	.h1-point-info {
		width: 250px;
		left: -070%;
	}

	.hl-point2 .h1-point-info {
		width: 250px;
		left: -510%;
	}

	.intro16 img {
		margin-top: 30px;
	}

	.intro16 h3 {
		font-size: 45px;
	}

	.intro17 {
		padding: 130px 0 120px;
	}

	.intro17 h3 {
		margin: 40px 0px 30px;
	}

	.intro18 p {
		padding: 0;
	}

	.intro18 h3 {
		font-size: 38px;
	}

	.intro19 h3 {
		font-size: 38px;
	}

	.intro19 p {
		padding: 0;
	}

	.app-btn a {
		float: none;
	}

	.intro2 h3 {
		font-size: 34px;
	}

	.intro2 p {
		font-size: 15px;
		padding: 0;
	}

	.dual-btn a {
		margin: 0 5px 15px;
	}

	.intro3 h3 {
		font-size: 37px;
	}

	.intro-newsletter input {
		width: 100%;
		margin-bottom: 15px;
	}

	.intro4 h2 {
		font-size: 40px;
		margin: -30px 0 15px;
	}

	.intro1 {
		padding: 120px 0 90px;
	}

	.intro4 p {
		padding: 0;
	}

	.intro4 {
		padding: 220px 0 67px;
		margin-bottom: 20px;
	}

	.intro5 p {
		font-size: 15px;
		padding: 0;
	}

	.intro6 h2 {
		font-size: 45px;
	}

	.intro6 p {
		font-size: 15px;
		padding: 0;
	}

	.intro6 p.lead {
		font-size: 28px;
	}

	.intro6 p.lead a {
		color: #fff;
		display: block;
	}

	.intro6 {
		padding: 100px 0;
	}

	.intro6 .container, .intro6 .row {
		height: auto;
	}

	.intro6 {
		padding: 100px 0;
		display: table;
	}

	.intro7 h2 {
		margin: 30px 0 15px;
	}

	.intro7 {
		padding: 120px 0 90px;
	}

	.intro8 h3 {
		font-size: 33px;
		line-height: 43px;
	}

	.intro8 img {
		width: 150%;
		margin-top: 50px;
	}

	.intro9 h3 {
		font-size: 33px;
		line-height: 43px;
	}

	.intro9 img {
		border: 20px solid #000;
		border-bottom: none;
		border-radius: 15px;
		max-width: 90%;
		margin: 0 5% -30px;
	}

	.intro10 h3 {
		margin: 40px 0px 25px;
	}

	.intro10 {
		padding: 120px 0 70px;
	}

	.intro10-nl {
		width: 100%;
		margin: 0;
	}

	.intro11 h3 {
		font-size: 36px;
	}

	.intro11 {
		padding: 130px 0 100px;
	}

	.intro12 h3 {
		font-size: 34px;
	}

	.intro12 {
		padding: 160px 0 100px;
	}

	.intro12 p {
		padding: 0;
	}

	.intro13 {
		padding: 130px 0 100px;
	}

	.intro13 h3 {
		margin: 30px 0px 30px;
		font-size: 33px;
	}

	.intro14 h3 {
		font-size: 33px;
	}

	.intro14 p {
		padding: 0;
	}

	.features-list li i {
		width: 60px;
		height: 60px;
		line-height: 58px;
		font-size: 23px;
	}

	.intro15 h3 {
		font-size: 45px;
		line-height: 54px;
	}

	.hl-container {
		margin-top: 40px;
	}

	.intro1 h2 {
		font-size: 34px;
		margin: 40px 0 15px;
	}

	.info-content img {
		width: 130%;
	}

	.testimonials p {
		font-size: 20px;
		padding: 0 15px 20px;
	}

	.ai-list ul li i {
		font-size: 40px;
		margin-bottom: 5px;
	}

	.video-box {
		display: block;
	}

	.ai-list ul li {
		display: inline-block;
		color: #43121d;
		min-width: 83px;
		padding: 0 10px;
		font-weight: 700;
		font-size: 12px;
		line-height: 30px;
		border-left: 1px solid #ddd;
		text-transform: uppercase;
		letter-spacing: 0.2px;
	}

	.pricing-item {
		margin: 0 0 30px;
		padding: 30px 30px 30px;
	}

	.pricing-item__popular {
		margin: 70px 0 30px;
	}

	.portfolio-filter li {
		margin: 0 5px 20px;
	}

	.portfolio-item {
		width: 100%;
	}

	.portfolio-2col .portfolio-item {
		width: 100%;
	}

	.portfolio-3col .portfolio-item {
		width: 100%;
	}

	.portfolio-4col .portfolio-item {
		width: 100%;
	}

	.team {
		padding: 100px 0 70px;
	}

	.table-wrap {
		width: 100%;
		overflow: auto;
		overflow-y: hidden;
	}

	.table-wrap table {
		width: 850px;
	}

	.nav-tabs > li > a {
		font-size: 12px;
		padding: 10px 9px;
	}

	.icon-box {
		padding: 100px 0 70px;
	}

	.service-box {
		margin-bottom: 30px;
	}

	.service2 {
		padding: 100px 0 70px;
	}

	.service2 .col-md-4 {
		margin-bottom: 30px;
	}

	#contact-info .col-sm-6 {
		margin-bottom: 20px;
	}

	.sidebar {
		padding-top: 50px;
	}

	.pagination > li > a {
		border-radius: 5px;
		margin: 0 1px;
		width: 35px;
		height: 35px;
		text-align: center;
		line-height: 37px;
		padding: 0;
	}

	.sub-about .text-right {
		text-align: left;
	}

	.sub-about p {
		margin-bottom: 30px;
	}

	.team .no-padding {
		padding: 0 15px !important;
	}

	.team .team-box {
		margin-bottom: 30px;
	}

	.team {
		padding: 100px 0 70px;
	}

	.navbar-login {
		display: none;
	}

	.navbar-nav > li > .dropdown-menu {
		margin-top: 0px;
		margin-left: 13px;
		border: none;
	}

	.dropdown-menu > li > a {
		display: block;
		padding: 6px 30px;
		font-size: 11px;
		color: #fff;
		text-transform: uppercase;
		border: none;
	}

	.navbar-nav > li > a:hover,
.dropdown-menu > li > a:hover {
		color: #a82d49 !important;
		background: transparent;
	}

	.navbar-nav > li > .dropdown-menu {
		margin-bottom: 15px;
	}

	.navbar-lg .navbar-nav > li > a {
		padding: 12px 30px;
		font-size: 12px;
	}

	.navbar-toggle {
		position: relative;
		float: right;
		padding: 0;
		margin-top: -16px;
		margin-right: 15px;
		margin-bottom: 8px;
		background: transparent !important;
		background-image: none;
		border: none !important;
		border-radius: 4px;
	}

	.navbar-toggle .icon-bar {
		display: block;
		width: 20px;
		height: 1px;
		margin-bottom: 4px;
		border-radius: 0px !important;
		background: #000 !important;
		transition: .4s;
	}

	.navbar-toggle .icon-bar:nth-child(3) {
		display: block;
		width: 16px;
		height: 1px;
		border-radius: 0px !important;
		background: #000 !important;
	}

	.navbar-toggle:hover .icon-bar:nth-child(3) {
		width: 20px;
	}

	.navbar-collapse {
		padding-right: 15px;
		padding-left: 15px;
		overflow-x: hidden;
		-webkit-overflow-scrolling: touch;
		border-top: none;
		box-shadow: none;
		position: absolute;
		background: #262626;
		top: 80px;
		width: 100%;
		max-width: 500px;
		right: 0;
		margin: 0 auto !important;
		left: 0;
	}

	.navbar-nav > li > a::after {
		display: none;
	}

	.navbar-default .navbar-nav > li {
		margin-left: 0;
		width: 100%;
		float: none !important;
		display: table;
	}

	.navbar-default .navbar-nav > li a {
		padding: 7px 30px;
	}

	.dropdown-menu {
		display: table !important;
		padding: 0;
		margin: 0;
		border-radius: 0px;
		background: transparent;
		top: 0px !important;
		left: 0 !important;
		right: auto !important;
		opacity: 1;
		position: relative !important;
		visibility: visible;
		transition: .4s;
		margin-left: 15px;
		border: none;
		box-shadow: none;
		width: 100%;
	}

	.navbar-default .navbar-nav > li a {
		padding: 7px 30px;
		border: none !important;
	}

	.navbar-lg .navbar-brand  {
		padding: 15px 15px 0;
	}

	.navbar-brand > img {
    max-width: 80px;
	}
	
	.error-content h4 {
		font-size: 60px;
		letter-spacing: -3px;
	}

	.error-content {
		padding: 70px 0;
	}

	.info-content h3 {
		font-size: 28px;
		margin: 40px 0 20px;
	}

	.info-content .btn.btn-lg {
		font-size: 11px;
		margin-bottom: 50px;
	}

	footer .list-inline li {
		width: 100%;
		border-bottom: 1px solid #fff;
	}

	footer .list-inline li:last-child {
		border: none;
	}

	.contact-info {
		padding: 30px 0;
		border: none;
	}

	.footer-logo {
		line-height: 40px;
		padding: 0 0 30px;
		text-align: center;
	}

	.footer-social {
		padding-left: 0;
		margin: 0 auto;
		display: table;
	}

	footer {
		padding: 60px 0 30px;
	}

	.page_head h3 {
		font-size: 45px;
	}

	.navbar-lg .navbar-brand {
		font-size: 22px;
	}
}

@media only screen and (min-width: 993px)  and (max-width: 4000px) {

	.center-content {
		display: flex;
		align-items: center;
		flex-direction: row;
	}
}
.footerP a{
	color:#fff;
	padding: 0 30px 0 0;
}

.footerP a:hover{
	color:#fff;
	text-decoration: underline;
}
